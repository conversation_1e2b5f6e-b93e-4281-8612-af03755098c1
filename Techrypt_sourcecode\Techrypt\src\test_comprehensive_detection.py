#!/usr/bin/env python3
"""
Test Comprehensive Service Detection - ANY business input should be detected
"""

import requests
import json

def test_comprehensive_detection():
    """Test if ANY business-related input is correctly detected and mapped to services"""
    
    base_url = "http://localhost:5000"
    
    # Test cases covering ALL possible business inputs
    test_cases = [
        # Website Development
        {"input": "website development", "expected": "Website Development"},
        {"input": "web design", "expected": "Website Development"},
        {"input": "online store", "expected": "Website Development"},
        {"input": "ecommerce site", "expected": "Website Development"},
        {"input": "seo optimization", "expected": "Website Development"},
        
        # Social Media Marketing
        {"input": "social media marketing", "expected": "Social Media Marketing"},
        {"input": "Instagram growth", "expected": "Social Media Marketing"},
        {"input": "Facebook marketing", "expected": "Social Media Marketing"},
        {"input": "content creation", "expected": "Social Media Marketing"},
        {"input": "social campaigns", "expected": "Social Media Marketing"},
        
        # Branding Services
        {"input": "logo designing", "expected": "Branding Services"},
        {"input": "graphic design", "expected": "Branding Services"},
        {"input": "brand identity", "expected": "Branding Services"},
        {"input": "visual design", "expected": "Branding Services"},
        {"input": "business cards", "expected": "Branding Services"},
        
        # Chatbot Development
        {"input": "chatbot development", "expected": "Chatbot Development"},
        {"input": "customer service automation", "expected": "Chatbot Development"},
        {"input": "ai assistant", "expected": "Chatbot Development"},
        {"input": "virtual assistant", "expected": "Chatbot Development"},
        
        # Automation Packages
        {"input": "business automation", "expected": "Automation Packages"},
        {"input": "email marketing", "expected": "Automation Packages"},
        {"input": "workflow automation", "expected": "Automation Packages"},
        {"input": "process automation", "expected": "Automation Packages"},
        
        # Payment Gateway
        {"input": "payment gateway", "expected": "Payment Gateway"},
        {"input": "payment processing", "expected": "Payment Gateway"},
        {"input": "online payments", "expected": "Payment Gateway"},
        {"input": "stripe integration", "expected": "Payment Gateway"},
        
        # General
        {"input": "services", "expected": "Service List"},
        {"input": "hello", "expected": "Business Greeting"},
        {"input": "tell me a joke", "expected": "Business Redirect"}
    ]
    
    print("🎯 Testing Comprehensive Service Detection")
    print("=" * 80)
    print("Testing if ANY business input is detected and mapped correctly...")
    print("=" * 80)
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/{total_count}:")
        print(f"Input: '{test_case['input']}'")
        print(f"Expected: {test_case['expected']}")
        
        try:
            payload = {"message": test_case['input'], "name": ""}
            response = requests.post(f"{base_url}/chat", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '')
                
                print(f"Response: {ai_response[:150]}...")
                
                # Check if correct service is detected
                response_lower = ai_response.lower()
                detected = False
                
                if test_case['expected'] == "Website Development":
                    if "website development" in response_lower or "custom website" in response_lower:
                        detected = True
                elif test_case['expected'] == "Social Media Marketing":
                    if "social media marketing" in response_lower or "social media" in response_lower:
                        detected = True
                elif test_case['expected'] == "Branding Services":
                    if "branding services" in response_lower or "brand identities" in response_lower:
                        detected = True
                elif test_case['expected'] == "Chatbot Development":
                    if "chatbot development" in response_lower or "customer service automation" in response_lower:
                        detected = True
                elif test_case['expected'] == "Automation Packages":
                    if "automation packages" in response_lower or "business processes" in response_lower:
                        detected = True
                elif test_case['expected'] == "Payment Gateway":
                    if "payment gateway" in response_lower or "payment" in response_lower:
                        detected = True
                elif test_case['expected'] == "Service List":
                    if "here are our main services" in response_lower:
                        detected = True
                elif test_case['expected'] == "Business Greeting":
                    if "welcome to techrypt" in response_lower or "what type of business" in response_lower:
                        detected = True
                elif test_case['expected'] == "Business Redirect":
                    if "business and digital marketing services" in response_lower:
                        detected = True
                
                if detected:
                    print("✅ CORRECT SERVICE DETECTED")
                    success_count += 1
                else:
                    print("❌ INCORRECT OR GENERIC RESPONSE")
                    
            else:
                print(f"❌ ERROR - HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ ERROR - {e}")
        
        print("-" * 80)
    
    print(f"\n🏆 FINAL RESULTS:")
    print(f"✅ Successful detections: {success_count}/{total_count}")
    print(f"📊 Success rate: {(success_count/total_count)*100:.1f}%")
    
    if success_count >= total_count * 0.8:  # 80% success rate
        print("🎉 EXCELLENT! Comprehensive service detection working!")
    elif success_count >= total_count * 0.6:  # 60% success rate
        print("⚠️ GOOD but needs improvement")
    else:
        print("❌ POOR - Major improvements needed")

if __name__ == "__main__":
    test_comprehensive_detection()
