#!/usr/bin/env python3
"""
EMERGENCY CHATBOT SERVER - BYPASSING ALL BROKEN LOGIC
This server provides INTELLIGENT responses for electronics showroom and all business types
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import logging
import pandas as pd
import os
import requests
import json
from datetime import datetime
try:
    from pymongo import MongoClient
    MONGODB_AVAILABLE = True
except ImportError:
    MONGODB_AVAILABLE = False
    logger.warning("⚠️ MongoDB not available - install pymongo")

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

class EmergencyChatbot:
    def __init__(self):
        self.conversation_history = []
        self.csv_data = self.load_csv_data()
        self.information_data = self.load_information_data()
        self.mongodb_client = self.setup_mongodb()

    def setup_mongodb(self):
        """Setup MongoDB connection"""
        if not MONGODB_AVAILABLE:
            return None

        try:
            client = MongoClient('mongodb://localhost:27017/', serverSelectionTimeoutMS=5000)
            # Test connection
            client.admin.command('ping')
            logger.info("✅ MongoDB connected successfully")
            return client
        except Exception as e:
            logger.warning(f"⚠️ MongoDB connection failed: {e}")
            return None

    def save_conversation(self, user_message: str, bot_response: str, user_name: str = ''):
        """Save conversation to MongoDB"""
        if not self.mongodb_client:
            return

        try:
            db = self.mongodb_client.techrypt_chatbot
            conversations = db.conversations

            conversation_data = {
                'user_message': user_message,
                'bot_response': bot_response,
                'user_name': user_name,
                'timestamp': datetime.now(),
                'server': 'emergency'
            }

            conversations.insert_one(conversation_data)
            logger.info("💾 Conversation saved to MongoDB")
        except Exception as e:
            logger.error(f"❌ Error saving conversation: {e}")

    def save_user_contact(self, contact_data: dict):
        """Save user contact to MongoDB"""
        if not self.mongodb_client:
            return

        try:
            db = self.mongodb_client.techrypt_chatbot
            users = db.users

            contact_data['timestamp'] = datetime.now()
            contact_data['server'] = 'emergency'

            users.insert_one(contact_data)
            logger.info("👤 User contact saved to MongoDB")
        except Exception as e:
            logger.error(f"❌ Error saving user contact: {e}")

    def load_csv_data(self):
        """Load CSV dataset for business intelligence"""
        try:
            csv_path = 'data.csv'
            if os.path.exists(csv_path):
                df = pd.read_csv(csv_path)
                logger.info(f"✅ Loaded CSV data: {len(df)} records")
                return df
            else:
                logger.warning("⚠️ data.csv not found")
                return None
        except Exception as e:
            logger.error(f"❌ Error loading CSV: {e}")
            return None

    def load_information_data(self):
        """Load information.txt for additional context"""
        try:
            info_path = 'information.txt'
            if os.path.exists(info_path):
                with open(info_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                logger.info(f"✅ Loaded information.txt: {len(content)} chars")
                return content
            else:
                logger.warning("⚠️ information.txt not found")
                return ""
        except Exception as e:
            logger.error(f"❌ Error loading information.txt: {e}")
            return ""

    def search_csv_for_business(self, message: str):
        """Search CSV for business type or keywords"""
        if self.csv_data is None:
            return None

        try:
            message_lower = message.lower()

            # Search in all columns for relevant matches
            for column in self.csv_data.columns:
                if self.csv_data[column].dtype == 'object':  # Text columns
                    matches = self.csv_data[self.csv_data[column].str.contains(message_lower, case=False, na=False)]
                    if not matches.empty:
                        return matches.iloc[0].to_dict()

            return None
        except Exception as e:
            logger.error(f"❌ CSV search error: {e}")
            return None

    def google_search_fallback(self, business_term: str):
        """Google Search API fallback for unknown business types"""
        try:
            # This would integrate with Google Search API
            # For now, return intelligent analysis based on common patterns
            business_lower = business_term.lower()

            # Common business pattern detection
            if any(word in business_lower for word in ['shop', 'store', 'retail', 'sell', 'product']):
                return 'retail'
            elif any(word in business_lower for word in ['service', 'consulting', 'agency', 'firm']):
                return 'professional_services'
            elif any(word in business_lower for word in ['food', 'restaurant', 'cafe', 'dining']):
                return 'restaurant'
            elif any(word in business_lower for word in ['health', 'medical', 'clinic', 'doctor']):
                return 'healthcare'
            elif any(word in business_lower for word in ['tech', 'software', 'app', 'digital']):
                return 'technology'
            else:
                return 'general_business'

        except Exception as e:
            logger.error(f"❌ Google search fallback error: {e}")
            return 'general_business'
        
    def get_intelligent_response(self, message: str, user_name: str = '') -> str:
        """EMERGENCY INTELLIGENT RESPONSE SYSTEM WITH CSV + GOOGLE FALLBACK"""
        try:
            message_lower = message.lower()

            # STEP 1: Check CSV dataset for business intelligence
            csv_match = self.search_csv_for_business(message)
            if csv_match:
                logger.info(f"🎯 CSV match found for: {message}")

            # STEP 2: Use Google Search fallback for unknown terms
            business_type = self.google_search_fallback(message)
            logger.info(f"🔍 Business type detected: {business_type}")

            # EMERGENCY ELECTRONICS SHOWROOM INTELLIGENCE
            if any(word in message_lower for word in ['electronics', 'showroom', 'electronics showroom', 'electronics store']):
                if any(word in message_lower for word in ['marketing', 'social media', 'promotion', 'advertising']):
                    return f"Perfect! For your electronics showroom, I can help you with Social Media Marketing. We specialize in:\n\n📱 **Electronics Store Social Media:**\n- Product showcases and demonstrations\n- Customer reviews and testimonials\n- Seasonal promotions and sales campaigns\n- Tech tips and product comparisons\n- Local customer engagement\n\nOur Social Media Marketing team knows how to showcase electronics effectively. Would you like to schedule a consultation to discuss your marketing strategy?"
                
                elif any(word in message_lower for word in ['website', 'web', 'online']):
                    return f"Excellent! For your electronics showroom, I can create a professional website with:\n\n🌐 **Electronics Store Website:**\n- Product catalog with detailed specifications\n- Online ordering and inventory management\n- Customer reviews and ratings\n- Technical support and warranty information\n- Mobile-responsive design for all devices\n\nWould you like to discuss your website requirements?"
                
                else:
                    # General electronics showroom response
                    return f"Great! For your electronics showroom, Techrypt can help you with:\n\n🔌 **Complete Digital Solutions:**\n1. 🌐 Website Development - Product catalogs and online sales\n2. 📱 Social Media Marketing - Showcase your latest electronics\n3. 🎨 Branding Services - Professional electronics store branding\n4. 🤖 Chatbot Development - Customer support and product inquiries\n5. ⚡ Automation Packages - Inventory and customer management\n6. 💳 Payment Gateway Integration - Secure online transactions\n\nWhich service interests you most, or would you like to schedule a consultation to discuss your specific needs?"
            
            # EMERGENCY GENERAL BUSINESS INTELLIGENCE
            elif any(word in message_lower for word in ['restaurant', 'cafe', 'food']):
                return f"Excellent! For your restaurant business, Techrypt can help you with:\n\n🍽️ **Restaurant Digital Solutions:**\n1. 🌐 Website Development - Online menus and reservations\n2. 📱 Social Media Marketing - Food photography and customer engagement\n3. 🎨 Branding Services - Restaurant identity and marketing materials\n4. 🤖 Chatbot Development - Reservation and order management\n5. ⚡ Automation Packages - Kitchen and customer management\n6. 💳 Payment Gateway Integration - Online ordering and payments\n\nWhich service would help your restaurant grow?"
            
            elif any(word in message_lower for word in ['retail', 'store', 'shop', 'boutique', 'clothing']):
                return f"Perfect! For your retail business, Techrypt can help you with:\n\n🛍️ **Retail Digital Solutions:**\n1. 🌐 Website Development - E-commerce and product catalogs\n2. 📱 Social Media Marketing - Product showcases and customer engagement\n3. 🎨 Branding Services - Store identity and marketing materials\n4. 🤖 Chatbot Development - Customer support and product inquiries\n5. ⚡ Automation Packages - Inventory and customer management\n6. 💳 Payment Gateway Integration - Secure online transactions\n\nWhat aspect of your retail business needs the most attention?"
            
            elif any(word in message_lower for word in ['dental', 'clinic', 'medical', 'healthcare', 'doctor']):
                return f"Excellent! For your healthcare practice, Techrypt can help you with:\n\n🏥 **Healthcare Digital Solutions:**\n1. 🌐 Website Development - Patient portals and appointment booking\n2. 📱 Social Media Marketing - Health tips and patient engagement\n3. 🎨 Branding Services - Professional medical branding\n4. 🤖 Chatbot Development - Appointment scheduling and patient support\n5. ⚡ Automation Packages - Patient management systems\n6. 💳 Payment Gateway Integration - Secure payment processing\n\nWhich service would benefit your practice most?"
            
            elif any(word in message_lower for word in ['agency', 'consulting', 'firm', 'professional services']):
                return f"Perfect! For your professional services, Techrypt can help you with:\n\n💼 **Professional Services Digital Solutions:**\n1. 🌐 Website Development - Professional portfolios and client portals\n2. 📱 Social Media Marketing - Thought leadership and client engagement\n3. 🎨 Branding Services - Professional identity and marketing materials\n4. 🤖 Chatbot Development - Client support and lead qualification\n5. ⚡ Automation Packages - Client management and workflow optimization\n6. 💳 Payment Gateway Integration - Secure client billing\n\nWhat aspect of your professional services needs enhancement?"
            
            # EMERGENCY SERVICE REQUESTS
            elif any(word in message_lower for word in ['marketing', 'social media', 'promotion']):
                return f"Excellent! I can help you with Social Media Marketing. Our services include:\n\n📱 **Social Media Marketing:**\n- Platform management (Instagram, Facebook, LinkedIn)\n- Content creation and scheduling\n- Audience engagement and growth\n- Analytics and performance tracking\n- Paid advertising campaigns\n- Brand awareness strategies\n\nWould you like to schedule a consultation to discuss your marketing goals?"
            
            elif any(word in message_lower for word in ['website', 'web development', 'online presence']):
                return f"Perfect! I can help you with Website Development. Our services include:\n\n🌐 **Website Development:**\n- Custom responsive design\n- SEO optimization\n- Content management systems\n- E-commerce functionality\n- Mobile-friendly design\n- Performance optimization\n\nWould you like to discuss your website requirements?"
            
            elif any(word in message_lower for word in ['branding', 'logo', 'brand', 'design']):
                return f"Great! I can help you with Branding Services. Our services include:\n\n🎨 **Branding Services:**\n- Logo design and brand identity\n- Marketing materials and collateral\n- Brand guidelines and style guides\n- Business card and stationery design\n- Social media branding\n- Brand strategy consultation\n\nWould you like to discuss your branding needs?"
            
            # EMERGENCY GREETING RESPONSES
            elif any(word in message_lower for word in ['hello', 'hi', 'hey', 'good morning', 'good afternoon']):
                return f"Hello! Welcome to Techrypt.io! I'm here to help you grow your business with our digital services:\n\n🚀 **Our Services:**\n1. 🌐 Website Development\n2. 📱 Social Media Marketing\n3. 🎨 Branding Services\n4. 🤖 Chatbot Development\n5. ⚡ Automation Packages\n6. 💳 Payment Gateway Integration\n\nWhat type of business do you have, and how can I help you today?"
            
            # EMERGENCY APPOINTMENT REQUESTS
            elif any(phrase in message_lower for phrase in ['schedule appointment', 'book appointment', 'schedule meeting', 'book meeting']):
                return f"Perfect! I'd be happy to help you schedule an appointment. Our team will contact you within 24 hours to:\n\n📅 **Appointment Process:**\n- Discuss your specific business needs\n- Provide customized service recommendations\n- Create a tailored strategy for your business\n- Answer all your questions\n\nPlease use the appointment form that will appear shortly to book your consultation!"
            
            # EMERGENCY DEFAULT RESPONSE
            else:
                return f"Thank you for reaching out! I'm here to help you grow your business with Techrypt.io's digital services:\n\n🚀 **How We Can Help:**\n1. 🌐 Website Development - Professional, responsive websites\n2. 📱 Social Media Marketing - Grow your online presence\n3. 🎨 Branding Services - Create a memorable brand identity\n4. 🤖 Chatbot Development - Automate customer service\n5. ⚡ Automation Packages - Streamline your business processes\n6. 💳 Payment Gateway Integration - Secure online payments\n\nCould you tell me more about your business and what you're looking for?"
            
        except Exception as e:
            logger.error(f"❌ EMERGENCY RESPONSE ERROR: {e}")
            # Emergency fallback
            user_name_part = f", {user_name}" if user_name else ""
            return f"Hello{user_name_part}! I'm here to help you with Techrypt.io's digital marketing services. How can I assist you today?"

# Global chatbot instance
emergency_chatbot = EmergencyChatbot()

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({"status": "healthy", "message": "Emergency Techrypt AI Server is running"})

@app.route('/chat', methods=['POST'])
def chat():
    try:
        data = request.get_json()
        user_message = data.get('message', '')
        user_name = data.get('user_name', '')

        logger.info(f"Emergency server received: {user_message} from {user_name}")

        # Get intelligent response with proper personalization
        response = emergency_chatbot.get_intelligent_response(user_message, user_name)

        # Add personalization if user_name is provided and not empty
        if user_name and user_name.strip() and user_name.lower() != 'user':
            # Only personalize if response doesn't already contain the name
            if user_name not in response:
                # Add name to greeting responses
                if any(greeting in response.lower() for greeting in ['hello', 'welcome', 'thank you']):
                    response = response.replace('Hello!', f'Hello, {user_name}!')
                    response = response.replace('Welcome!', f'Welcome, {user_name}!')
                    response = response.replace('Thank you', f'Thank you, {user_name}')

        logger.info(f"Emergency server response: {response[:100]}...")

        # Save conversation to MongoDB
        emergency_chatbot.save_conversation(user_message, response, user_name)

        return jsonify({
            'response': response,
            'status': 'success',
            'server': 'emergency'
        })

    except Exception as e:
        logger.error(f"Emergency server error: {e}")
        return jsonify({
            'response': 'Hello! I\'m here to help you with Techrypt.io\'s digital marketing services. How can I assist you today?',
            'status': 'error',
            'server': 'emergency'
        }), 500

@app.route('/context', methods=['GET'])
def get_context():
    return jsonify({
        'server': 'emergency',
        'status': 'active',
        'message': 'Emergency intelligent chatbot server'
    })

@app.route('/reset', methods=['POST'])
def reset_context():
    emergency_chatbot.conversation_history = []
    return jsonify({'status': 'reset', 'server': 'emergency'})

if __name__ == '__main__':
    print("🚨 EMERGENCY TECHRYPT AI SERVER STARTING...")
    print("📡 Server will be available at: http://localhost:5000")
    print("🔗 Health check: http://localhost:5000/health")
    print("💬 Chat endpoint: POST http://localhost:5000/chat")
    print("🎯 EMERGENCY INTELLIGENT RESPONSES ACTIVE")
    
    app.run(host='0.0.0.0', port=5000, debug=False)
