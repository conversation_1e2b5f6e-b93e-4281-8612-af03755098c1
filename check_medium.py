#!/usr/bin/env python3
"""
Check and Download DialoGPT-medium
"""

import os
import sys

def setup_environment():
    """Setup download environment"""
    print("Setting up environment for DialoGPT-medium...")
    
    # Clear offline modes
    if 'TRANSFORMERS_OFFLINE' in os.environ:
        del os.environ['TRANSFORMERS_OFFLINE']
    if 'HF_HUB_OFFLINE' in os.environ:
        del os.environ['HF_HUB_OFFLINE']
    
    # Set long timeouts
    os.environ['HF_HUB_DOWNLOAD_TIMEOUT'] = '3600'  # 1 hour
    os.environ['CURL_CA_BUNDLE'] = ''
    os.environ['REQUESTS_CA_BUNDLE'] = ''
    
    print("Environment ready")

def check_medium_status():
    """Check DialoGPT-medium status"""
    print("DIALOGPT-MEDIUM STATUS CHECK")
    print("=" * 40)
    
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        
        model_name = 'microsoft/DialoGPT-medium'
        
        # Test if cached
        print("Checking if DialoGPT-medium is cached...")
        try:
            tokenizer = AutoTokenizer.from_pretrained(model_name, local_files_only=True)
            model = AutoModelForCausalLM.from_pretrained(model_name, local_files_only=True)
            print("SUCCESS: DialoGPT-medium is FULLY CACHED and ready!")
            return True
        except Exception as e:
            print(f"Not cached: {str(e)[:100]}...")
            return False
            
    except Exception as e:
        print(f"Error checking status: {e}")
        return False

def download_medium():
    """Download DialoGPT-medium"""
    print("\nDOWNLOADING DIALOGPT-MEDIUM")
    print("=" * 40)
    
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        import torch
        
        model_name = 'microsoft/DialoGPT-medium'
        
        print("Step 1: Downloading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(
            model_name,
            resume_download=True
        )
        print("Tokenizer completed")
        
        print("Step 2: Downloading model (this will take time)...")
        print("Model size: ~860MB")
        print("Expected time: 1-3 hours depending on connection")
        
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            resume_download=True,
            torch_dtype=torch.float32,
            low_cpu_mem_usage=True
        )
        print("Model download completed!")
        
        print("Step 3: Testing model...")
        from transformers import pipeline
        pipe = pipeline('text-generation', model=model, tokenizer=tokenizer)
        result = pipe("Hello", max_length=20)
        print(f"Test successful: {result[0]['generated_text'][:50]}")
        
        return True
        
    except Exception as e:
        print(f"Download failed: {e}")
        return False

def main():
    """Main function"""
    print("DIALOGPT-MEDIUM CHECKER AND DOWNLOADER")
    print("=" * 50)
    
    setup_environment()
    
    # Check current status
    is_cached = check_medium_status()
    
    if is_cached:
        print("\nDialoGPT-medium is ready to use!")
    else:
        print("\nDialoGPT-medium not found. Starting download...")
        success = download_medium()
        
        if success:
            print("\nSUCCESS: DialoGPT-medium is now ready!")
        else:
            print("\nFAILED: DialoGPT-medium download unsuccessful")
    
    print("\nCheck completed")

if __name__ == "__main__":
    main()
