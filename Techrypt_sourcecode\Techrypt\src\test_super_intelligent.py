#!/usr/bin/env python3
"""
Test Super-Intelligent Platform Detection with Web Search + LLM
"""

import requests
import json

def test_super_intelligent_platform_detection():
    """Test if platforms are intelligently analyzed with web search + LLM"""
    
    base_url = "http://localhost:5000"
    
    # Test cases for platform intelligence
    test_cases = [
        # E-commerce Platforms
        {"input": "shopify", "expected": "E-commerce Platform Analysis"},
        {"input": "daraz", "expected": "E-commerce Platform Analysis"},
        {"input": "amazon seller", "expected": "E-commerce Platform Analysis"},
        {"input": "etsy shop", "expected": "E-commerce Platform Analysis"},
        {"input": "woocommerce", "expected": "E-commerce Platform Analysis"},
        
        # Social Media Platforms
        {"input": "instagram business", "expected": "Social Media Platform Analysis"},
        {"input": "facebook ads", "expected": "Social Media Platform Analysis"},
        {"input": "youtube channel", "expected": "Social Media Platform Analysis"},
        {"input": "tiktok business", "expected": "Social Media Platform Analysis"},
        
        # Payment Platforms
        {"input": "stripe", "expected": "Payment Platform Analysis"},
        {"input": "paypal", "expected": "Payment Platform Analysis"},
        {"input": "square", "expected": "Payment Platform Analysis"},
        
        # CMS Platforms
        {"input": "wordpress", "expected": "CMS Platform Analysis"},
        {"input": "wix", "expected": "CMS Platform Analysis"},
        {"input": "squarespace", "expected": "CMS Platform Analysis"},
        
        # Unknown/New Platforms (should use web search)
        {"input": "bigcommerce", "expected": "Unknown Platform Analysis"},
        {"input": "prestashop", "expected": "Unknown Platform Analysis"},
        
        # Regular service detection (should still work)
        {"input": "logo designing", "expected": "Branding Services"},
        {"input": "website development", "expected": "Website Development"},
        {"input": "services", "expected": "Service List"}
    ]
    
    print("🧠 Testing Super-Intelligent Platform Detection")
    print("=" * 80)
    print("Testing web search + LLM analysis for ANY platform...")
    print("=" * 80)
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/{total_count}:")
        print(f"Input: '{test_case['input']}'")
        print(f"Expected: {test_case['expected']}")
        
        try:
            payload = {"message": test_case['input'], "name": ""}
            response = requests.post(f"{base_url}/chat", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '')
                
                print(f"Response: {ai_response[:200]}...")
                
                # Check if correct analysis is provided
                response_lower = ai_response.lower()
                detected = False
                
                if test_case['expected'] == "E-commerce Platform Analysis":
                    if ("e-commerce" in response_lower or "ecommerce" in response_lower or 
                        "online store" in response_lower or "store optimization" in response_lower or
                        "sell products" in response_lower):
                        detected = True
                        
                elif test_case['expected'] == "Social Media Platform Analysis":
                    if ("social media" in response_lower or "brand awareness" in response_lower or
                        "audience targeting" in response_lower or "content creation" in response_lower):
                        detected = True
                        
                elif test_case['expected'] == "Payment Platform Analysis":
                    if ("payment processing" in response_lower or "payment integration" in response_lower or
                        "secure payments" in response_lower or "checkout" in response_lower):
                        detected = True
                        
                elif test_case['expected'] == "CMS Platform Analysis":
                    if ("website building" in response_lower or "website platform" in response_lower or
                        "online presence" in response_lower or "custom development" in response_lower):
                        detected = True
                        
                elif test_case['expected'] == "Unknown Platform Analysis":
                    if ("understand you're working with" in response_lower or 
                        "help you determine" in response_lower or
                        "business goals" in response_lower):
                        detected = True
                        
                elif test_case['expected'] == "Branding Services":
                    if "branding services" in response_lower or "brand identities" in response_lower:
                        detected = True
                        
                elif test_case['expected'] == "Website Development":
                    if "website development" in response_lower or "custom website" in response_lower:
                        detected = True
                        
                elif test_case['expected'] == "Service List":
                    if "here are our main services" in response_lower:
                        detected = True
                
                # Check if response is platform-specific (not generic)
                is_specific = False
                if (test_case['input'] in response_lower or 
                    "specifically help with" in response_lower or
                    "custom" in response_lower or
                    "optimization" in response_lower):
                    is_specific = True
                
                if detected and is_specific:
                    print("✅ INTELLIGENT PLATFORM ANALYSIS DETECTED")
                    success_count += 1
                elif detected:
                    print("⚠️ CORRECT CATEGORY BUT NOT SPECIFIC ENOUGH")
                else:
                    print("❌ INCORRECT OR GENERIC RESPONSE")
                    
            else:
                print(f"❌ ERROR - HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ ERROR - {e}")
        
        print("-" * 80)
    
    print(f"\n🏆 FINAL RESULTS:")
    print(f"✅ Intelligent platform analyses: {success_count}/{total_count}")
    print(f"📊 Success rate: {(success_count/total_count)*100:.1f}%")
    
    if success_count >= total_count * 0.8:  # 80% success rate
        print("🎉 EXCELLENT! Super-intelligent platform detection working!")
    elif success_count >= total_count * 0.6:  # 60% success rate
        print("⚠️ GOOD but needs improvement")
    else:
        print("❌ POOR - Major improvements needed")

if __name__ == "__main__":
    test_super_intelligent_platform_detection()
