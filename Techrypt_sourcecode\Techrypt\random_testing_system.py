#!/usr/bin/env python3
"""
Advanced Random Testing & Improvement System for Techrypt AI Chatbot
Continuously tests and improves chatbot performance with random scenarios
"""

import requests
import random
import time
import json
import logging
from datetime import datetime
from typing import List, Dict, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TechryptRandomTester:
    def __init__(self, server_url="http://localhost:5000"):
        self.server_url = server_url
        self.test_results = []
        self.improvement_suggestions = []
        
        # Comprehensive test scenarios
        self.business_types = [
            "restaurant", "bakery", "cafe", "food truck", "catering",
            "salon", "spa", "barbershop", "nail salon", "beauty parlor",
            "gym", "fitness center", "yoga studio", "health club",
            "clinic", "dental office", "medical practice", "pharmacy",
            "store", "boutique", "retail shop", "clothing store",
            "agency", "consulting firm", "law firm", "accounting firm",
            "construction", "plumbing", "electrical", "carpentry",
            "auto repair", "car dealership", "mechanic", "garage",
            "real estate", "property management", "realtor",
            "plant nursery", "garden center", "landscaping",
            "egg business", "dairy farm", "organic farm",
            "bread business", "pastry shop", "donut shop",
            "jewelry store", "watch repair", "electronics store",
            "pet store", "veterinary clinic", "dog grooming",
            "hotel", "bed and breakfast", "vacation rental",
            "photography studio", "wedding planning", "event planning",
            "cleaning service", "laundry service", "dry cleaning",
            "tutoring center", "music school", "art studio",
            "printing shop", "copy center", "graphic design",
            "software company", "IT services", "web design",
            "marketing agency", "advertising firm", "PR company"
        ]
        
        self.service_requests = [
            "website development", "web design", "website", "online presence",
            "social media marketing", "SMM", "Instagram", "Facebook", "social media",
            "branding services", "logo design", "brand identity", "branding",
            "chatbot development", "AI chatbot", "customer service bot", "automation",
            "automation packages", "business automation", "workflow automation",
            "payment gateway", "payment integration", "Stripe", "PayPal", "checkout"
        ]
        
        self.platforms = [
            "Shopify", "Etsy", "Amazon", "eBay", "WooCommerce", "Magento",
            "WordPress", "Squarespace", "Wix", "BigCommerce"
        ]
        
        self.conversation_starters = [
            "hello", "hi", "hey", "good morning", "good afternoon",
            "I need help", "can you help me", "what services do you offer",
            "tell me about your services", "I'm interested in your services"
        ]
        
        self.complex_scenarios = [
            "I have a {business} and need {service1}, {service2}, and {service3}",
            "My {business} needs help with {service1} and {service2}",
            "I want to take my {business} online with {platform}",
            "Can you help my {business} with {service1}?",
            "I'm looking for {service1} for my {business}",
            "My {business} needs a complete digital transformation",
            "I want to grow my {business} online",
            "Help me modernize my {business}",
            "I need {service1} and {service2} for my {business}",
            "Can you integrate {platform} with my {business}?"
        ]

    def generate_random_test_case(self) -> Tuple[str, str, Dict]:
        """Generate a random test case with expected behavior"""
        
        test_type = random.choice([
            "greeting", "business_inquiry", "service_request", 
            "multiple_services", "platform_integration", "complex_scenario"
        ])
        
        if test_type == "greeting":
            message = random.choice(self.conversation_starters)
            expected = {
                "should_contain": ["welcome", "techrypt", "help"],
                "should_not_contain": ["error", "sorry"],
                "quality_threshold": 70
            }
            
        elif test_type == "business_inquiry":
            business = random.choice(self.business_types)
            message = f"I have a {business}"
            expected = {
                "should_contain": [business, "help", "services"],
                "should_not_contain": ["error", "cannot assist"],
                "quality_threshold": 80
            }
            
        elif test_type == "service_request":
            service = random.choice(self.service_requests)
            message = f"I need {service}"
            expected = {
                "should_contain": [service.split()[0], "help", "consultation"],
                "should_not_contain": ["error", "don't understand"],
                "quality_threshold": 85
            }
            
        elif test_type == "multiple_services":
            services = random.sample(self.service_requests, 3)
            business = random.choice(self.business_types)
            message = f"I have a {business} and need {services[0]}, {services[1]}, and {services[2]}"
            expected = {
                "should_contain": [business, "combination", "integrated"],
                "should_not_contain": ["error", "garbled"],
                "quality_threshold": 90
            }
            
        elif test_type == "platform_integration":
            platform = random.choice(self.platforms)
            business = random.choice(self.business_types)
            message = f"I want to set up {platform} for my {business}"
            expected = {
                "should_contain": [platform.lower(), business, "help"],
                "should_not_contain": ["error", "don't know"],
                "quality_threshold": 85
            }
            
        else:  # complex_scenario
            scenario = random.choice(self.complex_scenarios)
            business = random.choice(self.business_types)
            services = random.sample(self.service_requests, 3)
            platform = random.choice(self.platforms)
            
            message = scenario.format(
                business=business,
                service1=services[0],
                service2=services[1],
                service3=services[2],
                platform=platform
            )
            expected = {
                "should_contain": [business, "help", "consultation"],
                "should_not_contain": ["error", "confused"],
                "quality_threshold": 85
            }
        
        return message, test_type, expected

    def send_test_message(self, message: str, user_name: str = "") -> Dict:
        """Send test message to chatbot and get response"""
        try:
            response = requests.post(
                f"{self.server_url}/chat",
                json={"message": message, "user_name": user_name},
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"HTTP {response.status_code}"}
                
        except Exception as e:
            return {"error": str(e)}

    def evaluate_response(self, response: str, expected: Dict) -> Dict:
        """Evaluate response quality against expected criteria"""
        response_lower = response.lower()
        score = 0
        issues = []
        
        # Check required content
        for item in expected["should_contain"]:
            if item.lower() in response_lower:
                score += 20
            else:
                issues.append(f"Missing required content: {item}")
        
        # Check forbidden content
        for item in expected["should_not_contain"]:
            if item.lower() in response_lower:
                score -= 30
                issues.append(f"Contains forbidden content: {item}")
        
        # Check response length (not too short or too long)
        if 50 <= len(response) <= 1000:
            score += 10
        else:
            issues.append(f"Response length issue: {len(response)} chars")
        
        # Check for consultation offer
        if any(word in response_lower for word in ["consultation", "schedule", "discuss"]):
            score += 15
        
        # Check for Techrypt branding
        if "techrypt" in response_lower:
            score += 10
        
        # Check for garbled text
        if any(phrase in response_lower for phrase in ["will ypur", "ypur", "chatbot, will"]):
            score -= 40
            issues.append("Garbled text detected")
        
        # Ensure score is within bounds
        score = max(0, min(100, score))
        
        return {
            "score": score,
            "passed": score >= expected["quality_threshold"],
            "issues": issues
        }

    def run_random_test_batch(self, num_tests: int = 20) -> Dict:
        """Run a batch of random tests"""
        print(f"\n🎯 RUNNING {num_tests} RANDOM TESTS")
        print("=" * 60)
        
        results = {
            "total_tests": num_tests,
            "passed": 0,
            "failed": 0,
            "average_score": 0,
            "test_details": [],
            "improvement_areas": []
        }
        
        total_score = 0
        
        for i in range(num_tests):
            # Generate random test case
            message, test_type, expected = self.generate_random_test_case()
            
            print(f"\n🔍 Test {i+1}/{num_tests}: {test_type}")
            print(f"Message: \"{message}\"")
            
            # Send message and get response
            response_data = self.send_test_message(message)
            
            if "error" in response_data:
                print(f"❌ ERROR: {response_data['error']}")
                results["failed"] += 1
                continue
            
            response = response_data.get("response", "")
            print(f"Response: {response[:100]}...")
            
            # Evaluate response
            evaluation = self.evaluate_response(response, expected)
            total_score += evaluation["score"]
            
            if evaluation["passed"]:
                results["passed"] += 1
                status = "✅ PASS"
            else:
                results["failed"] += 1
                status = "❌ FAIL"
            
            print(f"{status} Score: {evaluation['score']}%")
            
            if evaluation["issues"]:
                print(f"Issues: {', '.join(evaluation['issues'])}")
                results["improvement_areas"].extend(evaluation["issues"])
            
            # Store detailed results
            results["test_details"].append({
                "test_number": i + 1,
                "message": message,
                "test_type": test_type,
                "response": response,
                "score": evaluation["score"],
                "passed": evaluation["passed"],
                "issues": evaluation["issues"]
            })
            
            time.sleep(0.5)  # Brief pause between tests
        
        results["average_score"] = total_score / num_tests if num_tests > 0 else 0
        
        return results

    def analyze_results_and_suggest_improvements(self, results: Dict) -> List[str]:
        """Analyze test results and suggest specific improvements"""
        suggestions = []
        
        # Overall performance analysis
        pass_rate = (results["passed"] / results["total_tests"]) * 100
        avg_score = results["average_score"]
        
        print(f"\n📊 PERFORMANCE ANALYSIS")
        print("=" * 40)
        print(f"Pass Rate: {pass_rate:.1f}%")
        print(f"Average Score: {avg_score:.1f}%")
        
        if pass_rate < 70:
            suggestions.append("🔴 CRITICAL: Pass rate below 70% - Major improvements needed")
        elif pass_rate < 85:
            suggestions.append("🟡 WARNING: Pass rate below 85% - Moderate improvements needed")
        else:
            suggestions.append("🟢 GOOD: Pass rate above 85% - Minor optimizations needed")
        
        # Analyze common issues
        all_issues = results["improvement_areas"]
        issue_counts = {}
        for issue in all_issues:
            issue_counts[issue] = issue_counts.get(issue, 0) + 1
        
        # Sort issues by frequency
        sorted_issues = sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)
        
        print(f"\n🔍 TOP ISSUES TO FIX:")
        for issue, count in sorted_issues[:5]:
            print(f"• {issue} ({count} occurrences)")
            
            # Specific improvement suggestions
            if "garbled text" in issue.lower():
                suggestions.append("Fix text generation logic to prevent garbled responses")
            elif "missing required content" in issue.lower():
                suggestions.append("Improve content detection and response generation")
            elif "response length" in issue.lower():
                suggestions.append("Optimize response length for better user experience")
        
        return suggestions

    def continuous_testing_loop(self, iterations: int = 5, tests_per_iteration: int = 15):
        """Run continuous testing and improvement cycles"""
        print(f"\n🚀 STARTING CONTINUOUS TESTING SYSTEM")
        print(f"Iterations: {iterations}, Tests per iteration: {tests_per_iteration}")
        print("=" * 70)
        
        all_results = []
        
        for iteration in range(iterations):
            print(f"\n🔄 ITERATION {iteration + 1}/{iterations}")
            print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # Run test batch
            results = self.run_random_test_batch(tests_per_iteration)
            all_results.append(results)
            
            # Analyze and suggest improvements
            suggestions = self.analyze_results_and_suggest_improvements(results)
            
            print(f"\n💡 IMPROVEMENT SUGGESTIONS:")
            for suggestion in suggestions:
                print(f"• {suggestion}")
            
            # Wait before next iteration
            if iteration < iterations - 1:
                print(f"\n⏳ Waiting 30 seconds before next iteration...")
                time.sleep(30)
        
        # Final summary
        self.generate_final_report(all_results)

    def generate_final_report(self, all_results: List[Dict]):
        """Generate comprehensive final report"""
        print(f"\n📋 FINAL COMPREHENSIVE REPORT")
        print("=" * 50)
        
        total_tests = sum(r["total_tests"] for r in all_results)
        total_passed = sum(r["passed"] for r in all_results)
        avg_scores = [r["average_score"] for r in all_results]
        
        print(f"Total Tests Run: {total_tests}")
        print(f"Overall Pass Rate: {(total_passed/total_tests)*100:.1f}%")
        print(f"Average Score Trend: {avg_scores}")
        print(f"Score Improvement: {avg_scores[-1] - avg_scores[0]:.1f} points")
        
        # Performance trend
        if avg_scores[-1] > avg_scores[0]:
            print("📈 TREND: Performance IMPROVING")
        elif avg_scores[-1] < avg_scores[0]:
            print("📉 TREND: Performance DECLINING")
        else:
            print("📊 TREND: Performance STABLE")
        
        # Save detailed report
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(all_results, f, indent=2)
        
        print(f"\n💾 Detailed report saved to: {report_file}")

if __name__ == "__main__":
    tester = TechryptRandomTester()
    
    # Run continuous testing
    tester.continuous_testing_loop(iterations=3, tests_per_iteration=10)
