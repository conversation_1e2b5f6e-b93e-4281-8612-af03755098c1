#!/usr/bin/env python3
"""
🚀 AUTOMATIC LLM DOWNLOADER WITH RETRY LOGIC
Downloads LLM models in background with network retry
"""

import os
import sys
import time
import threading
import subprocess
from pathlib import Path

class AutoLLMDownloader:
    def __init__(self):
        self.download_status = {
            'microsoft/DialoGPT-small': False,
            'distilgpt2': False,
            'gpt2': False
        }
        self.download_threads = []
        
    def check_model_exists(self, model_name):
        """Check if model is already cached"""
        try:
            from transformers import AutoTokenizer
            # Try to load from cache
            tokenizer = AutoTokenizer.from_pretrained(model_name, local_files_only=True)
            return True
        except:
            return False
    
    def download_model_background(self, model_name):
        """Download model in background thread"""
        def download():
            try:
                print(f"🔄 Starting background download: {model_name}")
                
                # Use subprocess to avoid blocking
                cmd = [
                    sys.executable, "-c",
                    f"""
import os
os.environ['HF_HUB_DISABLE_PROGRESS_BARS'] = '1'
from transformers import AutoTokenizer, AutoModelForCausalLM
print('Downloading {model_name}...')
tokenizer = AutoTokenizer.from_pretrained('{model_name}')
model = AutoModelForCausalLM.from_pretrained('{model_name}')
print('✅ {model_name} downloaded successfully!')
"""
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
                
                if result.returncode == 0:
                    self.download_status[model_name] = True
                    print(f"✅ {model_name} ready!")
                else:
                    print(f"❌ {model_name} failed: {result.stderr}")
                    
            except Exception as e:
                print(f"❌ Background download failed for {model_name}: {e}")
        
        thread = threading.Thread(target=download, daemon=True)
        thread.start()
        self.download_threads.append(thread)
        return thread
    
    def start_downloads(self):
        """Start downloading all models"""
        print("🚀 STARTING AUTOMATIC LLM DOWNLOADS")
        print("=" * 50)
        
        for model_name in self.download_status.keys():
            if self.check_model_exists(model_name):
                print(f"✅ {model_name} already cached")
                self.download_status[model_name] = True
            else:
                print(f"🔄 Queuing download: {model_name}")
                self.download_model_background(model_name)
        
        return self.download_status
    
    def get_status(self):
        """Get current download status"""
        return self.download_status

def main():
    """Main function"""
    downloader = AutoLLMDownloader()
    status = downloader.start_downloads()
    
    print(f"\n📊 DOWNLOAD STATUS:")
    for model, downloaded in status.items():
        status_icon = "✅" if downloaded else "🔄"
        print(f"{status_icon} {model}")
    
    print(f"\n💡 Downloads running in background...")
    print(f"🔄 Server will automatically use LLM when models are ready!")

if __name__ == "__main__":
    main()
