@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Bebas+Neue&display=swap');
/* @import "tailwindcss"; */
@tailwind base;
@tailwind components;
@tailwind utilities;
/* @tailwind base;
@tailwind components;
@tailwind utilities;
@theme {
  --color-five:#0158A6;
  --color-six:#121212;
  --color-seven:#3D550C;
  --color-eight:#FF56A5;
  --color-nine:#0a0f14;
  --color-ten:#CFE5FF;
  --color-primary:#AEBB1E;
  --color-black: #0f0f0f;
} */
.glowing-yellow{
  filter: drop-shadow(0 0 7px #F5FF1E);
}
.glowing-green{
  filter: drop-shadow(0 0 7px #D4FF00);
}
.glowing-pink{
  filter: drop-shadow(0 0 7px #FF56A5);
}
.glow-green {
  box-shadow: 
    0 0 35px rgba(212, 255, 0, 0.4), 
    0 0 17px rgba(212, 255, 0, 0.3), 
    0 0 10px rgba(174, 187, 30, 0.2);
}

.glow-hover:hover {
  box-shadow: 
    0 0 35px rgba(212, 255, 0, 0.4), 
    0 0 17px rgba(212, 255, 0, 0.3), 
    0 0 10px rgba(174, 187, 30, 0.2);
}

.glow-pink {
  box-shadow: 
    0 0 35px rgba(255, 86, 165, 0.4), 
    0 0 17px rgba(255, 86, 165, 0.3), 
    0 0 10px rgba(255, 86, 165, 0.2);
}
.fading{
  height: 167px;
  width: 100%;
  z-index: 50;
  margin: -100px 0px 0px 0px;
  position: relative;
  background: linear-gradient(0deg, rgba(15, 15, 15, 1) 50%, rgba(31, 31, 31, 0) 100%);
}
.roboto {
  font-family: "Roboto", sans-serif;
}
.inter {
  font-family: "Inter", sans-serif;
}
.bebas {
  font-family: "Bebas Neue", sans-serif;
}
body{
  scroll-behavior: smooth;
}
* {
  margin: 0;
  padding: 0;
  font-family: "Right Grotesk", sans-serif;
}

@font-face {
  font-family: "Right Grotesk";
  src: url("/path/to/RightGrotesk-Regular.woff2") format("woff2"),
    url("/path/to/RightGrotesk-Regular.woff") format("woff");
  font-weight: normal;
  font-style: normal;
}

body {
  font-family: "Right Grotesk", sans-serif;
}

:root {
  --color-black: #0f0f0f;
  --color-black-2: #0d0d0d;
  --color-white: #fff;
  --color-yellow: #aebb1e;
  --color-blue: #9bd4d7;
  --tr-main-tf: cubic-bezier(0.36, 0.3, 0, 1);
  --tr-main-dur: 300ms;
}

::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background-color: black;
}

::-webkit-scrollbar-thumb {
  background-color: var(--color-white);
  border-radius: 4px;
}

html {
  scroll-behavior: smooth;
}

.slide-in {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: #0d0d0d;
  transform-origin: bottom;
}

.slide-out {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: #0d0d0d;
  transform-origin: top;
}
