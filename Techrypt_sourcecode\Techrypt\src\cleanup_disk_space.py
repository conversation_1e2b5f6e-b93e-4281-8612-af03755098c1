#!/usr/bin/env python3
"""
🧹 DISK SPACE CLEANUP UTILITY
Cleans up disk space to allow medium model loading
"""

import os
import shutil
import tempfile
import subprocess
import sys
from pathlib import Path

def get_disk_usage():
    """Get current disk usage"""
    try:
        total, used, free = shutil.disk_usage("C:\\")
        return {
            'total_gb': total // (1024**3),
            'used_gb': used // (1024**3),
            'free_gb': free // (1024**3)
        }
    except Exception as e:
        print(f"❌ Error checking disk usage: {e}")
        return None

def clean_temp_files():
    """Clean temporary files"""
    cleaned_mb = 0
    
    temp_dirs = [
        tempfile.gettempdir(),
        "C:\\Windows\\Temp",
        os.path.expanduser("~/AppData/Local/Temp")
    ]
    
    for temp_dir in temp_dirs:
        if os.path.exists(temp_dir):
            try:
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        try:
                            file_path = os.path.join(root, file)
                            file_size = os.path.getsize(file_path)
                            os.remove(file_path)
                            cleaned_mb += file_size / (1024*1024)
                        except:
                            continue
                print(f"✅ Cleaned temp files: {cleaned_mb:.1f} MB")
            except Exception as e:
                print(f"⚠️ Could not clean {temp_dir}: {e}")
    
    return cleaned_mb

def clean_huggingface_cache():
    """Clean Hugging Face model cache"""
    cleaned_mb = 0
    
    cache_dirs = [
        os.path.expanduser("~/.cache/huggingface"),
        os.path.expanduser("~/AppData/Local/huggingface"),
        "C:\\Users\\<USER>\\.cache\\huggingface"
    ]
    
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir):
            try:
                # Calculate size before deletion
                total_size = 0
                for root, dirs, files in os.walk(cache_dir):
                    for file in files:
                        total_size += os.path.getsize(os.path.join(root, file))
                
                shutil.rmtree(cache_dir)
                cleaned_mb += total_size / (1024*1024)
                print(f"✅ Cleaned Hugging Face cache: {cleaned_mb:.1f} MB")
            except Exception as e:
                print(f"⚠️ Could not clean {cache_dir}: {e}")
    
    return cleaned_mb

def clean_pip_cache():
    """Clean pip cache"""
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "cache", "purge"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Cleaned pip cache")
            return True
    except Exception as e:
        print(f"⚠️ Could not clean pip cache: {e}")
    return False

def clean_browser_cache():
    """Clean browser cache (Chrome/Edge)"""
    cleaned_mb = 0
    
    browser_cache_dirs = [
        os.path.expanduser("~/AppData/Local/Google/Chrome/User Data/Default/Cache"),
        os.path.expanduser("~/AppData/Local/Microsoft/Edge/User Data/Default/Cache"),
        os.path.expanduser("~/AppData/Local/Mozilla/Firefox/Profiles/*/cache2")
    ]
    
    for cache_dir in browser_cache_dirs:
        if os.path.exists(cache_dir):
            try:
                total_size = 0
                for root, dirs, files in os.walk(cache_dir):
                    for file in files:
                        try:
                            file_path = os.path.join(root, file)
                            total_size += os.path.getsize(file_path)
                            os.remove(file_path)
                        except:
                            continue
                cleaned_mb += total_size / (1024*1024)
            except Exception as e:
                print(f"⚠️ Could not clean browser cache: {e}")
    
    if cleaned_mb > 0:
        print(f"✅ Cleaned browser cache: {cleaned_mb:.1f} MB")
    
    return cleaned_mb

def empty_recycle_bin():
    """Empty recycle bin"""
    try:
        import winshell
        winshell.recycle_bin().empty(confirm=False, show_progress=False, sound=False)
        print("✅ Emptied recycle bin")
        return True
    except ImportError:
        try:
            # Alternative method using PowerShell
            subprocess.run([
                "powershell", "-Command", 
                "Clear-RecycleBin -Force -ErrorAction SilentlyContinue"
            ], capture_output=True)
            print("✅ Emptied recycle bin")
            return True
        except Exception as e:
            print(f"⚠️ Could not empty recycle bin: {e}")
    except Exception as e:
        print(f"⚠️ Could not empty recycle bin: {e}")
    return False

def main():
    """Main cleanup function"""
    print("🧹 DISK SPACE CLEANUP UTILITY")
    print("=" * 50)
    
    # Check initial disk usage
    initial_usage = get_disk_usage()
    if initial_usage:
        print(f"💾 Initial disk space: {initial_usage['free_gb']} GB free / {initial_usage['total_gb']} GB total")
    
    total_cleaned_mb = 0
    
    print("\n🔄 Starting cleanup...")
    
    # Clean temporary files
    print("\n1. Cleaning temporary files...")
    total_cleaned_mb += clean_temp_files()
    
    # Clean Hugging Face cache
    print("\n2. Cleaning Hugging Face cache...")
    total_cleaned_mb += clean_huggingface_cache()
    
    # Clean pip cache
    print("\n3. Cleaning pip cache...")
    clean_pip_cache()
    
    # Clean browser cache
    print("\n4. Cleaning browser cache...")
    total_cleaned_mb += clean_browser_cache()
    
    # Empty recycle bin
    print("\n5. Emptying recycle bin...")
    empty_recycle_bin()
    
    # Check final disk usage
    final_usage = get_disk_usage()
    if final_usage and initial_usage:
        freed_gb = final_usage['free_gb'] - initial_usage['free_gb']
        print(f"\n✅ CLEANUP COMPLETE!")
        print(f"💾 Final disk space: {final_usage['free_gb']} GB free")
        print(f"🎯 Space freed: {freed_gb:.1f} GB ({total_cleaned_mb/1024:.1f} GB)")
        
        if final_usage['free_gb'] >= 1:
            print("🚀 Sufficient space available for medium model!")
        else:
            print("⚠️ Still low on space. Consider:")
            print("   • Moving files to external drive")
            print("   • Uninstalling unused programs")
            print("   • Using cloud storage")
    
    print(f"\n🎯 RECOMMENDATIONS:")
    print("• For medium model: Need ~1GB free space")
    print("• Current model priority: DialoGPT-small (460MB)")
    print("• Alternative: Use external drive for Hugging Face cache")

if __name__ == "__main__":
    main()
