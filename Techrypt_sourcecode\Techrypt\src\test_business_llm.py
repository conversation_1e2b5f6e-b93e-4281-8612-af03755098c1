#!/usr/bin/env python3
"""
Test Business-Focused LLM Intelligence
"""

import requests
import json

def test_business_intelligence():
    """Test if LLM intelligently detects service needs and explains how Techrypt can help"""
    
    base_url = "http://localhost:5000"
    
    test_cases = [
        {
            "input": "I need help with my website",
            "expected_service": "Website Development",
            "description": "Should detect website development need"
        },
        {
            "input": "how to grow Instagram followers",
            "expected_service": "Social Media Marketing",
            "description": "Should detect social media marketing need"
        },
        {
            "input": "I need a logo for my business",
            "expected_service": "Branding Services",
            "description": "Should detect branding/logo design need"
        },
        {
            "input": "can you help with customer service automation",
            "expected_service": "Chatbot Development",
            "description": "Should detect chatbot/automation need"
        },
        {
            "input": "I want to automate my email marketing",
            "expected_service": "Automation Packages",
            "description": "Should detect automation need"
        },
        {
            "input": "need help setting up online payments",
            "expected_service": "Payment Gateway",
            "description": "Should detect payment integration need"
        },
        {
            "input": "hello there",
            "expected_service": "General Business",
            "description": "Should redirect to business services"
        },
        {
            "input": "tell me a joke",
            "expected_service": "Business Redirect",
            "description": "Should redirect non-business questions to services"
        },
        {
            "input": "what do you think about AI",
            "expected_service": "Business Redirect",
            "description": "Should redirect to relevant business services"
        },
        {
            "input": "I have an online store",
            "expected_service": "Website Development",
            "description": "Should detect ecommerce/website needs"
        }
    ]
    
    print("🎯 Testing Business-Focused LLM Intelligence")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/{len(test_cases)}:")
        print(f"Input: '{test_case['input']}'")
        print(f"Expected Service: {test_case['expected_service']}")
        print(f"Description: {test_case['description']}")
        
        try:
            payload = {"message": test_case['input'], "name": ""}
            response = requests.post(f"{base_url}/chat", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '')
                
                print(f"Response: {ai_response[:200]}...")
                
                # Analyze if response is business-focused and service-specific
                response_lower = ai_response.lower()
                
                # Check for business focus
                business_indicators = [
                    'techrypt', 'help', 'service', 'consultation', 'business',
                    'website', 'social media', 'branding', 'chatbot', 'automation', 'payment'
                ]
                
                is_business_focused = any(indicator in response_lower for indicator in business_indicators)
                
                # Check for service-specific content
                service_keywords = {
                    "Website Development": ['website', 'site', 'web', 'ecommerce', 'seo'],
                    "Social Media Marketing": ['social', 'instagram', 'facebook', 'followers', 'content'],
                    "Branding Services": ['logo', 'brand', 'design', 'identity'],
                    "Chatbot Development": ['chatbot', 'automation', 'customer service', 'ai'],
                    "Automation Packages": ['automation', 'workflow', 'email', 'crm'],
                    "Payment Gateway": ['payment', 'checkout', 'stripe', 'paypal']
                }
                
                detected_services = []
                for service, keywords in service_keywords.items():
                    if any(keyword in response_lower for keyword in keywords):
                        detected_services.append(service)
                
                # Evaluate response quality
                if is_business_focused:
                    print("✅ BUSINESS-FOCUSED - Response relates to business services")
                else:
                    print("❌ NOT BUSINESS-FOCUSED - Response seems off-topic")
                
                if detected_services:
                    print(f"🎯 DETECTED SERVICES: {', '.join(detected_services)}")
                    if test_case['expected_service'] in detected_services or test_case['expected_service'] in ['General Business', 'Business Redirect']:
                        print("✅ CORRECT SERVICE DETECTION")
                    else:
                        print("⚠️ DIFFERENT SERVICE DETECTED")
                else:
                    print("❌ NO SPECIFIC SERVICE DETECTED")
                
                # Check for consultation offer
                if 'consultation' in response_lower or 'schedule' in response_lower or 'discuss' in response_lower:
                    print("✅ OFFERS CONSULTATION - Professional approach")
                else:
                    print("⚠️ NO CONSULTATION OFFER")
                    
            else:
                print(f"❌ ERROR - HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ ERROR - {e}")
        
        print("-" * 60)

if __name__ == "__main__":
    test_business_intelligence()
