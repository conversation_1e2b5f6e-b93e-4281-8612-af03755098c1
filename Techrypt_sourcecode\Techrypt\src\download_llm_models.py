#!/usr/bin/env python3
"""
🤖 AUTOMATIC LLM MODEL DOWNLOADER
Downloads and caches LLM models for the chatbot system
"""

import os
import sys
import time
from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline
import torch

def download_model(model_name, description):
    """Download a specific model with progress tracking"""
    print(f"\n🔄 Downloading {description}...")
    print(f"Model: {model_name}")
    print("=" * 60)
    
    try:
        start_time = time.time()
        
        # Download tokenizer first
        print("📥 Downloading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(
            model_name,
            cache_dir=None,  # Use default cache
            force_download=False,  # Use cached if available
            resume_download=True   # Resume interrupted downloads
        )
        print("✅ Tokenizer downloaded successfully!")
        
        # Download model
        print("📥 Downloading model...")
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            cache_dir=None,
            force_download=False,
            resume_download=True,
            torch_dtype=torch.float32,  # Use float32 for compatibility
            low_cpu_mem_usage=True      # Optimize memory usage
        )
        print("✅ Model downloaded successfully!")
        
        # Test the model
        print("🧪 Testing model...")
        test_pipeline = pipeline(
            'text-generation',
            model=model,
            tokenizer=tokenizer,
            max_length=50,
            do_sample=True,
            temperature=0.7
        )
        
        test_result = test_pipeline("Hello, I need help with", max_length=30)
        print(f"✅ Model test successful: {test_result[0]['generated_text'][:50]}...")
        
        end_time = time.time()
        duration = end_time - start_time
        print(f"⏱️ Download completed in {duration:.1f} seconds")
        
        return True
        
    except Exception as e:
        print(f"❌ Error downloading {model_name}: {e}")
        return False

def check_disk_space():
    """Check available disk space"""
    try:
        import shutil
        total, used, free = shutil.disk_usage("C:\\")
        free_gb = free // (1024**3)
        print(f"💾 Available disk space: {free_gb} GB")
        
        if free_gb < 1:
            print("⚠️ Warning: Low disk space! Models may fail to download.")
            return False
        return True
    except Exception as e:
        print(f"⚠️ Could not check disk space: {e}")
        return True

def main():
    """Main download function"""
    print("🤖 AUTOMATIC LLM MODEL DOWNLOADER")
    print("🎯 Downloading models for Techrypt AI Chatbot")
    print("=" * 80)
    
    # Check disk space
    if not check_disk_space():
        print("❌ Insufficient disk space. Please free up space and try again.")
        return
    
    # Models to download (in order of preference)
    models_to_download = [
        ("microsoft/DialoGPT-small", "DialoGPT Small (117MB) - Primary Model"),
        ("distilgpt2", "DistilGPT2 (82MB) - Backup Model"),
        ("gpt2", "GPT2 (124MB) - Fallback Model")
    ]
    
    successful_downloads = 0
    
    for model_name, description in models_to_download:
        success = download_model(model_name, description)
        if success:
            successful_downloads += 1
            print(f"🎉 {description} ready for use!")
        else:
            print(f"💥 {description} failed to download")
        
        # Small delay between downloads
        time.sleep(2)
    
    print(f"\n📊 DOWNLOAD SUMMARY:")
    print(f"✅ Successfully downloaded: {successful_downloads}/{len(models_to_download)} models")
    
    if successful_downloads > 0:
        print("🚀 LLM models are now available for the chatbot!")
        print("🔄 Restart the AI server to use LLM models")
        print("\nNext steps:")
        print("1. Stop the current server (Ctrl+C)")
        print("2. Run: py ai_server.py")
        print("3. The server will now use LLM models!")
    else:
        print("❌ No models downloaded successfully")
        print("💡 The system will continue using Enhanced CSV mode")
    
    print("\n🎯 Models will be cached for future use!")

if __name__ == "__main__":
    main()
