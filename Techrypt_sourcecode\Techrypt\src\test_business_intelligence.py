#!/usr/bin/env python3
"""
Test Business Intelligence - Restaurant, E-commerce, Service Business Analysis
"""

import requests
import json
import time

def test_business_intelligence():
    """Test intelligent business analysis for different business types"""
    
    base_url = "http://localhost:5000"
    
    # Test cases for different business types
    test_cases = [
        # Restaurant Businesses
        {"input": "kababjis", "type": "Restaurant", "expected": ["restaurant", "online ordering", "menu", "reservations"]},
        {"input": "burger king", "type": "Restaurant", "expected": ["restaurant", "food", "online ordering", "menu"]},
        {"input": "mcdonalds", "type": "Restaurant", "expected": ["restaurant", "fast food", "online ordering", "menu"]},
        {"input": "pizza hut", "type": "Restaurant", "expected": ["restaurant", "pizza", "online ordering", "delivery"]},
        {"input": "starbucks", "type": "Restaurant", "expected": ["coffee", "restaurant", "online ordering", "menu"]},
        
        # E-commerce Businesses
        {"input": "amazon", "type": "E-commerce", "expected": ["e-commerce", "online store", "payment", "inventory"]},
        {"input": "ebay", "type": "E-commerce", "expected": ["e-commerce", "marketplace", "payment", "selling"]},
        {"input": "etsy", "type": "E-commerce", "expected": ["e-commerce", "handmade", "online store", "payment"]},
        
        # Service Businesses
        {"input": "uber", "type": "Service", "expected": ["service", "booking", "customer", "automation"]},
        {"input": "airbnb", "type": "Service", "expected": ["service", "booking", "rental", "customer"]},
        {"input": "netflix", "type": "Service", "expected": ["streaming", "service", "subscription", "customer"]},
        
        # Retail Businesses
        {"input": "walmart", "type": "Retail", "expected": ["retail", "store", "e-commerce", "inventory"]},
        {"input": "target", "type": "Retail", "expected": ["retail", "store", "shopping", "inventory"]},
        
        # Tech/Software Platforms
        {"input": "zoom", "type": "Tech Platform", "expected": ["platform", "integration", "automation", "development"]},
        {"input": "slack", "type": "Tech Platform", "expected": ["platform", "integration", "automation", "communication"]},
    ]
    
    print("🧠 Testing Business Intelligence Analysis")
    print("=" * 80)
    print("Testing intelligent business type detection and industry-specific responses...")
    print("=" * 80)
    
    business_specific_count = 0
    industry_relevant_count = 0
    total_count = len(test_cases)
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/{total_count}:")
        print(f"Business: '{test_case['input']}'")
        print(f"Expected Type: {test_case['type']}")
        print(f"Expected Keywords: {test_case['expected']}")
        
        try:
            payload = {"message": test_case['input'], "name": ""}
            response = requests.post(f"{base_url}/chat", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '')
                
                print(f"Response: {ai_response[:200]}...")
                
                # Check if response is business-specific
                response_lower = ai_response.lower()
                business_name = test_case['input'].lower()
                
                # Check if business name is mentioned
                business_mentioned = business_name in response_lower
                
                # Check for industry-specific keywords
                industry_keywords_found = []
                for keyword in test_case['expected']:
                    if keyword in response_lower:
                        industry_keywords_found.append(keyword)
                
                # Check for generic vs specific responses
                generic_phrases = [
                    "custom solutions and integrations",
                    "promote your .* presence across all channels",
                    "professional branding for your .* business",
                    "streamline your .* workflows"
                ]
                
                is_generic = any(phrase in response_lower for phrase in generic_phrases)
                
                # Check for business-type specific features
                business_specific_features = {
                    "Restaurant": ["online ordering", "menu", "reservations", "food", "restaurant"],
                    "E-commerce": ["online store", "payment", "inventory", "e-commerce", "shopping"],
                    "Service": ["booking", "service", "customer", "automation", "scheduling"],
                    "Retail": ["store", "retail", "inventory", "shopping", "location"],
                    "Tech Platform": ["integration", "automation", "development", "platform", "api"]
                }
                
                expected_features = business_specific_features.get(test_case['type'], [])
                features_found = [f for f in expected_features if f in response_lower]
                
                # Scoring
                if business_mentioned and len(industry_keywords_found) >= 2 and len(features_found) >= 1 and not is_generic:
                    print("🌟 EXCELLENT - Business-specific with industry features")
                    business_specific_count += 1
                    industry_relevant_count += 1
                    score = "EXCELLENT"
                elif business_mentioned and len(industry_keywords_found) >= 1 and not is_generic:
                    print("✅ GOOD - Business-specific with some industry relevance")
                    business_specific_count += 1
                    score = "GOOD"
                elif len(features_found) >= 1 and not is_generic:
                    print("⚠️ PARTIAL - Some industry features but not fully specific")
                    industry_relevant_count += 1
                    score = "PARTIAL"
                elif business_mentioned and not is_generic:
                    print("🔶 BASIC - Business mentioned but not industry-specific")
                    business_specific_count += 1
                    score = "BASIC"
                else:
                    print("❌ GENERIC - Generic response, no business intelligence")
                    score = "GENERIC"
                
                results.append({
                    "business": test_case['input'],
                    "type": test_case['type'],
                    "response": ai_response[:150] + "...",
                    "score": score,
                    "business_mentioned": business_mentioned,
                    "industry_keywords": industry_keywords_found,
                    "features_found": features_found,
                    "is_generic": is_generic
                })
                    
            else:
                print(f"❌ ERROR - HTTP {response.status_code}")
                results.append({
                    "business": test_case['input'],
                    "type": test_case['type'],
                    "response": f"HTTP Error {response.status_code}",
                    "score": "ERROR",
                    "business_mentioned": False,
                    "industry_keywords": [],
                    "features_found": [],
                    "is_generic": True
                })
                
        except Exception as e:
            print(f"❌ ERROR - {e}")
            results.append({
                "business": test_case['input'],
                "type": test_case['type'],
                "response": f"Exception: {e}",
                "score": "ERROR",
                "business_mentioned": False,
                "industry_keywords": [],
                "features_found": [],
                "is_generic": True
            })
        
        print("-" * 80)
        time.sleep(2)  # Delay for web search
    
    # Final Results Summary
    print(f"\n🏆 BUSINESS INTELLIGENCE RESULTS:")
    print("=" * 80)
    print(f"🎯 Business-specific responses: {business_specific_count}/{total_count}")
    print(f"🏭 Industry-relevant responses: {industry_relevant_count}/{total_count}")
    print(f"📊 Business intelligence rate: {(business_specific_count/total_count)*100:.1f}%")
    print(f"🔥 Industry relevance rate: {(industry_relevant_count/total_count)*100:.1f}%")
    
    # Business type breakdown
    print(f"\n📋 BUSINESS TYPE BREAKDOWN:")
    types = {}
    for result in results:
        btype = result['type']
        if btype not in types:
            types[btype] = {'total': 0, 'business_specific': 0, 'industry_relevant': 0}
        types[btype]['total'] += 1
        if result['business_mentioned']:
            types[btype]['business_specific'] += 1
        if len(result['features_found']) > 0:
            types[btype]['industry_relevant'] += 1
    
    for btype, stats in types.items():
        business_rate = (stats['business_specific']/stats['total'])*100
        industry_rate = (stats['industry_relevant']/stats['total'])*100
        print(f"  {btype}: {stats['business_specific']}/{stats['total']} business-specific ({business_rate:.1f}%), {stats['industry_relevant']}/{stats['total']} industry-relevant ({industry_rate:.1f}%)")
    
    # Overall assessment
    if business_specific_count >= total_count * 0.7:  # 70% business-specific
        print("🎉 EXCELLENT! Business intelligence working amazingly!")
        if industry_relevant_count >= total_count * 0.6:  # 60% industry-relevant
            print("🚀 OUTSTANDING! Industry-specific features detected!")
    elif business_specific_count >= total_count * 0.5:  # 50% business-specific
        print("⚠️ GOOD but needs improvement for better industry intelligence")
    else:
        print("❌ POOR - Major improvements needed for business intelligence")
    
    return results

if __name__ == "__main__":
    results = test_business_intelligence()
    
    # Save results to file
    with open('business_intelligence_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    print(f"\n💾 Results saved to business_intelligence_results.json")
