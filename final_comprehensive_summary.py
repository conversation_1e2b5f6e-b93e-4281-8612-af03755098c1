#!/usr/bin/env python3
"""
FINAL COMPREHENSIVE SUMMARY TEST
Quick but thorough test of all critical scenarios for production readiness
"""

import requests
import json
import time

def final_comprehensive_summary():
    """Run final comprehensive test with key scenarios"""
    
    base_url = "http://localhost:5000"
    
    print("🎯 FINAL COMPREHENSIVE SUMMARY TEST")
    print("=" * 70)
    print("Testing all critical scenarios for production readiness")
    print("=" * 70)
    
    # Critical test scenarios covering everything
    test_scenarios = [
        # 1. GREETINGS (5 tests)
        {"input": "hello", "category": "greetings", "description": "Basic greeting"},
        {"input": "hi there", "category": "greetings", "description": "Casual greeting"},
        {"input": "", "category": "greetings", "description": "Empty message"},
        {"input": "help", "category": "greetings", "description": "Help request"},
        {"input": "what can you do", "category": "greetings", "description": "Capabilities question"},
        
        # 2. BUSINESS OWNERSHIP (10 tests)
        {"input": "I have a restaurant", "category": "business", "description": "Restaurant ownership"},
        {"input": "I own a bakery", "category": "business", "description": "Bakery ownership"},
        {"input": "My clothing store", "category": "business", "description": "Clothing store"},
        {"input": "beauty salon", "category": "business", "description": "Beauty salon"},
        {"input": "I run a gym", "category": "business", "description": "Gym business"},
        {"input": "dental clinic", "category": "business", "description": "Dental clinic"},
        {"input": "law firm", "category": "business", "description": "Law firm"},
        {"input": "online store", "category": "business", "description": "Online store"},
        {"input": "construction company", "category": "business", "description": "Construction company"},
        {"input": "real estate agency", "category": "business", "description": "Real estate agency"},
        
        # 3. SERVICE REQUESTS (10 tests)
        {"input": "I need a website", "category": "services", "description": "Website request"},
        {"input": "social media marketing", "category": "services", "description": "Social media service"},
        {"input": "logo design", "category": "services", "description": "Logo design service"},
        {"input": "chatbot development", "category": "services", "description": "Chatbot service"},
        {"input": "payment gateway", "category": "services", "description": "Payment service"},
        {"input": "business automation", "category": "services", "description": "Automation service"},
        {"input": "web scraping", "category": "services", "description": "Web scraping service"},
        {"input": "copywriting", "category": "services", "description": "Copywriting service"},
        {"input": "Instagram growth", "category": "services", "description": "Instagram service"},
        {"input": "SEO optimization", "category": "services", "description": "SEO service"},
        
        # 4. PLATFORM MENTIONS (8 tests)
        {"input": "Shopify store", "category": "platforms", "description": "Shopify platform"},
        {"input": "WordPress website", "category": "platforms", "description": "WordPress platform"},
        {"input": "Daraz store", "category": "platforms", "description": "Daraz platform"},
        {"input": "Instagram business", "category": "platforms", "description": "Instagram platform"},
        {"input": "Facebook ads", "category": "platforms", "description": "Facebook ads"},
        {"input": "Stripe payments", "category": "platforms", "description": "Stripe platform"},
        {"input": "Mailchimp campaigns", "category": "platforms", "description": "Mailchimp platform"},
        {"input": "Squarespace website", "category": "platforms", "description": "Squarespace platform"},
        
        # 5. MIXED SCENARIOS (12 tests) - MOST CRITICAL
        {"input": "I have a restaurant and need a website", "category": "mixed", "description": "Restaurant + website"},
        {"input": "My clothing store needs social media marketing", "category": "mixed", "description": "Store + social media"},
        {"input": "Dental clinic needs appointment booking", "category": "mixed", "description": "Clinic + booking"},
        {"input": "Gym business wants payment gateway", "category": "mixed", "description": "Gym + payment"},
        {"input": "Beauty salon needs Instagram growth", "category": "mixed", "description": "Salon + Instagram"},
        {"input": "Law firm wants professional website", "category": "mixed", "description": "Law firm + website"},
        {"input": "Online store needs better branding", "category": "mixed", "description": "E-commerce + branding"},
        {"input": "Photography business needs portfolio website", "category": "mixed", "description": "Photography + portfolio"},
        {"input": "Construction company wants lead generation", "category": "mixed", "description": "Construction + leads"},
        {"input": "Bakery business looking for social media help", "category": "mixed", "description": "Bakery + social media"},
        {"input": "Real estate agency needs property listings", "category": "mixed", "description": "Real estate + listings"},
        {"input": "Travel agency wants booking system", "category": "mixed", "description": "Travel + booking"},
        
        # 6. EDGE CASES (5 tests)
        {"input": "kidnapping business", "category": "edge", "description": "Illegal activity"},
        {"input": "tell me a joke", "category": "edge", "description": "Non-business request"},
        {"input": "asdfghjkl", "category": "edge", "description": "Gibberish input"},
        {"input": "what's the weather", "category": "edge", "description": "Off-topic question"},
        {"input": "how much does it cost", "category": "edge", "description": "Pricing question"},
    ]
    
    # Run tests by category
    results_by_category = {}
    total_tests = 0
    total_passed = 0
    
    for category in ["greetings", "business", "services", "platforms", "mixed", "edge"]:
        category_tests = [t for t in test_scenarios if t["category"] == category]
        category_passed = 0
        category_total = len(category_tests)
        
        print(f"\n🔍 {category.upper()} TESTS ({category_total} tests)")
        print("-" * 50)
        
        for i, test in enumerate(category_tests, 1):
            print(f"🧪 Test {i}: '{test['input'][:40]}{'...' if len(test['input']) > 40 else ''}'")
            
            result = run_test(base_url, test['input'], test['description'])
            
            if result['passed']:
                category_passed += 1
                total_passed += 1
                status = "✅ PASS"
            else:
                status = "❌ FAIL"
            
            total_tests += 1
            
            print(f"   {status} - {result['analysis']}")
            
            # Brief delay
            time.sleep(0.3)
        
        category_score = (category_passed / category_total) * 100 if category_total > 0 else 0
        results_by_category[category] = {
            'passed': category_passed,
            'total': category_total,
            'score': category_score
        }
        
        print(f"📊 {category.title()} Score: {category_passed}/{category_total} ({category_score:.1f}%)")
    
    # Generate final comprehensive report
    print("\n" + "=" * 70)
    print("🎯 FINAL COMPREHENSIVE SUMMARY REPORT")
    print("=" * 70)
    
    overall_score = (total_passed / total_tests) * 100 if total_tests > 0 else 0
    print(f"📊 OVERALL SCORE: {total_passed}/{total_tests} ({overall_score:.1f}%)")
    
    print(f"\n📈 CATEGORY BREAKDOWN:")
    for category, results in results_by_category.items():
        score = results['score']
        if score >= 90:
            status_icon = "🚀"
            status_text = "EXCELLENT"
        elif score >= 80:
            status_icon = "✅"
            status_text = "VERY GOOD"
        elif score >= 70:
            status_icon = "⚠️"
            status_text = "GOOD"
        else:
            status_icon = "❌"
            status_text = "NEEDS WORK"
        
        print(f"{status_icon} {category.title()}: {results['passed']}/{results['total']} ({score:.1f}%) - {status_text}")
    
    # Critical analysis for non-technical users
    print(f"\n🎯 NON-TECHNICAL USER READINESS:")
    
    mixed_score = results_by_category.get('mixed', {}).get('score', 0)
    business_score = results_by_category.get('business', {}).get('score', 0)
    services_score = results_by_category.get('services', {}).get('score', 0)
    
    critical_average = (mixed_score + business_score + services_score) / 3
    
    print(f"🔑 Critical Scenarios Average: {critical_average:.1f}%")
    print(f"   - Mixed Scenarios (Most Important): {mixed_score:.1f}%")
    print(f"   - Business Recognition: {business_score:.1f}%")
    print(f"   - Service Recognition: {services_score:.1f}%")
    
    # Final production recommendation
    print(f"\n🚀 PRODUCTION DEPLOYMENT RECOMMENDATION:")
    if overall_score >= 95:
        print("🎉 OUTSTANDING - Deploy immediately! System exceeds expectations")
    elif overall_score >= 90:
        print("🚀 EXCELLENT - Ready for immediate production deployment")
    elif overall_score >= 85:
        print("✅ VERY GOOD - Ready for production with confidence")
    elif overall_score >= 80:
        print("✅ GOOD - Ready for production")
    elif overall_score >= 75:
        print("⚠️ ACCEPTABLE - Ready with minor monitoring")
    else:
        print("❌ NOT READY - Needs improvements before production")
    
    # Specific recommendations for non-technical users
    if mixed_score >= 90:
        print("✅ Perfect for non-technical users asking complex questions")
    elif mixed_score >= 80:
        print("✅ Good for non-technical users with minor edge cases")
    else:
        print("⚠️ May struggle with complex non-technical user scenarios")
    
    return overall_score

def run_test(base_url, user_input, description):
    """Run a single test and analyze response"""
    try:
        payload = {"message": user_input, "name": ""}
        response = requests.post(f"{base_url}/chat", json=payload, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            ai_response = result.get('response', '')
            
            # Quick quality analysis
            if len(ai_response.strip()) < 10:
                return {'passed': False, 'analysis': 'Response too short'}
            
            # Check for business relevance
            business_terms = ['techrypt', 'service', 'business', 'help', 'website', 'social', 'marketing']
            if not any(term in ai_response.lower() for term in business_terms):
                return {'passed': False, 'analysis': 'Not business-relevant'}
            
            # Check for appropriate response to illegal content
            if 'kidnapping' in user_input.lower():
                if 'cannot assist' in ai_response.lower() or 'illegal' in ai_response.lower():
                    return {'passed': True, 'analysis': 'Properly rejected illegal content'}
                else:
                    return {'passed': False, 'analysis': 'Failed to reject illegal content'}
            
            # General quality check
            return {'passed': True, 'analysis': 'Good business response'}
            
        else:
            return {'passed': False, 'analysis': f'HTTP {response.status_code}'}
            
    except Exception as e:
        return {'passed': False, 'analysis': f'Error: {str(e)[:30]}...'}

if __name__ == "__main__":
    score = final_comprehensive_summary()
    print(f"\n🏁 Final Production Readiness Score: {score:.1f}%")
