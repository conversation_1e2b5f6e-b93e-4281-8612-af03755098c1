#!/usr/bin/env python3
"""
Test CSV Loading for Large Dataset with Fixed Parser
"""

import csv
import os

def test_fixed_csv_loading():
    """Test loading the large CSV file with fixed parser"""

    csv_path = os.path.join(os.path.dirname(__file__), '..', 'data.csv')

    print(f"Testing CSV file: {csv_path}")
    print(f"File exists: {os.path.exists(csv_path)}")

    training_data = []

    try:
        with open(csv_path, 'r', encoding='utf-8') as file:
            # Handle malformed CSV with quoted header
            first_line = file.readline().strip()
            print(f"First line: {first_line}")

            if first_line.startswith('"user_input,intent,entities,bot_response"'):
                print("Using manual CSV parser for malformed header")

                # Parse manually due to malformed header
                for line_num, line in enumerate(file):
                    line = line.strip()
                    if line:
                        # Parse CSV line manually
                        parts = []
                        current_part = ""
                        in_quotes = False
                        i = 0

                        while i < len(line):
                            char = line[i]
                            if char == '"':
                                if i + 1 < len(line) and line[i + 1] == '"':
                                    # Escaped quote
                                    current_part += '"'
                                    i += 2
                                else:
                                    # Toggle quote state
                                    in_quotes = not in_quotes
                                    i += 1
                            elif char == ',' and not in_quotes:
                                # Field separator
                                parts.append(current_part)
                                current_part = ""
                                i += 1
                            else:
                                current_part += char
                                i += 1

                        # Add last part
                        parts.append(current_part)

                        # Ensure we have 4 parts
                        while len(parts) < 4:
                            parts.append("")

                        training_data.append({
                            'question': parts[0].lower(),
                            'answer': parts[3],
                            'category': parts[1],
                            'keywords': parts[2].lower().split() if parts[2] else []
                        })

                        # Show first few examples
                        if line_num < 5:
                            print(f"Line {line_num}: {parts}")
                            print(f"  Question: {parts[0]}")
                            print(f"  Intent: {parts[1]}")
                            print(f"  Entities: {parts[2]}")
                            print(f"  Response: {parts[3][:100]}...")
                            print()

                        if line_num >= 10:  # Test first 10 lines
                            break

        print(f"\nSuccessfully loaded {len(training_data)} training examples")

        # Show some statistics
        categories = [item['category'] for item in training_data]
        unique_categories = set(categories)
        print(f"Unique categories: {unique_categories}")

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fixed_csv_loading()
