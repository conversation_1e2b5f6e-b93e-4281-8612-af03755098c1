#!/usr/bin/env python3
"""
Test Chatbot Without Name - Should Not Use Hardcoded Names
"""

import requests
import json

def test_no_name():
    """Test that chatbot doesn't use hardcoded names when no name is provided"""
    
    base_url = "http://localhost:5000"
    
    print("👤 Testing Chatbot Without Name")
    print("=" * 50)
    print("✅ Should NOT use any hardcoded names")
    print("❌ Should NOT say 'alex' or any other name")
    print("=" * 50)
    
    # Test cases without providing any name
    test_cases = [
        {"input": "hello", "description": "Greeting without name"},
        {"input": "hi", "description": "Hi without name"},
        {"input": "services", "description": "Services request without name"},
        {"input": "copywriting", "description": "Service request without name"},
        {"input": "I have a restaurant", "description": "Business mention without name"},
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/{len(test_cases)}: '{test_case['input']}'")
        print(f"   Expected: {test_case['description']}")
        
        try:
            # Send request WITHOUT any name
            payload = {"message": test_case['input'], "name": ""}
            response = requests.post(f"{base_url}/chat", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '')
                
                print(f"   Response: {ai_response[:150]}...")
                
                # Check for hardcoded names
                hardcoded_names = ['alex', 'zain', 'john', 'user', 'customer']
                found_names = []
                
                for name in hardcoded_names:
                    if name.lower() in ai_response.lower():
                        found_names.append(name)
                
                if found_names:
                    print(f"   ❌ HARDCODED NAME FOUND: {found_names}")
                else:
                    print("   ✅ CLEAN - No hardcoded names")
                
                # Check for proper greeting format
                if test_case['input'] in ['hello', 'hi']:
                    if ai_response.startswith('Hello!') or ai_response.startswith('Hello! Welcome'):
                        print("   ✅ PROPER GREETING - Starts correctly without name")
                    elif ', ' in ai_response[:20]:  # Check if there's a comma suggesting a name
                        print("   ⚠️ SUSPICIOUS - Greeting might contain name placeholder")
                    else:
                        print("   ✅ GREETING OK")
                
            else:
                print(f"   ❌ ERROR - HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ ERROR - {e}")
        
        print("-" * 50)
    
    print(f"\n🎯 NO-NAME TEST COMPLETE!")
    print("✅ Clean = No hardcoded names found")
    print("❌ Hardcoded = Found hardcoded names like 'alex', 'zain', etc.")
    print("⚠️ Suspicious = Might contain name placeholders")

if __name__ == "__main__":
    test_no_name()
