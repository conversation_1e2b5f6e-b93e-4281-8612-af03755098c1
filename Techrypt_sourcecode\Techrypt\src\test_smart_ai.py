#!/usr/bin/env python3
"""
Test Smart AI System
"""

import requests
import json
import time

def test_smart_ai():
    """Test the smart AI system"""
    
    base_url = "http://localhost:5000"
    
    # Test cases for smart AI
    test_cases = [
        {
            "input": "hello",
            "name": "",
            "expected": "greeting response"
        },
        {
            "input": "daraz",
            "name": "",
            "expected": "should recognize <PERSON><PERSON> as ecommerce and provide structured services"
        },
        {
            "input": "I have a restaurant",
            "name": "<PERSON>",
            "expected": "should provide restaurant-specific services with name"
        },
        {
            "input": "I need social media marketing",
            "name": "",
            "expected": "should focus on SMM service"
        },
        {
            "input": "what services do you offer",
            "name": "",
            "expected": "should list all 6 services"
        },
        {
            "input": "shopify store",
            "name": "<PERSON>",
            "expected": "should recognize Shopify as ecommerce"
        }
    ]
    
    print("🧠 Testing Smart AI System")
    print("=" * 50)
    
    # Reset conversation first
    try:
        reset_response = requests.post(f"{base_url}/reset")
        if reset_response.status_code == 200:
            print("✅ Conversation reset successful")
        else:
            print("❌ Reset failed")
    except:
        print("❌ Could not reset conversation")
    
    passed = 0
    total = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/{total}:")
        print(f"Input: '{test_case['input']}' (name: '{test_case['name']}')")
        print(f"Expected: {test_case['expected']}")
        
        try:
            # Send request
            payload = {
                "message": test_case['input'],
                "name": test_case['name']
            }
            
            response = requests.post(f"{base_url}/chat", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '')
                
                print(f"Response: {ai_response[:150]}...")
                
                # Check if response is good
                is_good = (
                    len(ai_response) > 30 and  # Substantial response
                    'techrypt' in ai_response.lower() and  # Mentions company
                    ('service' in ai_response.lower() or 'help' in ai_response.lower())  # Relevant
                )
                
                # Special checks
                if test_case['input'] == 'daraz' and ('ecommerce' in ai_response.lower() or 'store' in ai_response.lower()):
                    is_good = True
                elif test_case['name'] and test_case['name'] in ai_response:
                    is_good = True
                elif 'services' in test_case['input'] and ('1.' in ai_response or '2.' in ai_response):
                    is_good = True
                
                if is_good:
                    print("✅ PASS")
                    passed += 1
                else:
                    print("❌ FAIL - Response not good enough")
                    
            else:
                print(f"❌ FAIL - HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ FAIL - Error: {e}")
        
        time.sleep(1)  # Brief pause between tests
    
    print("\n" + "=" * 50)
    print(f"📊 SMART AI TEST RESULTS")
    print("=" * 50)
    print(f"✅ Passed: {passed}/{total} ({passed/total*100:.1f}%)")
    print(f"❌ Failed: {total-passed}/{total}")
    
    if passed >= total * 0.8:  # 80% pass rate
        print("🎉 EXCELLENT! Smart AI is working perfectly!")
    elif passed >= total * 0.6:  # 60% pass rate
        print("👍 GOOD! Smart AI is working well")
    else:
        print("⚠️ NEEDS WORK! Smart AI needs improvement")

if __name__ == "__main__":
    test_smart_ai()
