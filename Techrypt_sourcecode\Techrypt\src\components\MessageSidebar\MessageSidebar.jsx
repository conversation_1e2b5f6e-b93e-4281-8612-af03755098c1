import React, { useState } from "react";
import "./MessageSidebar.css";
import { BsRobot } from "react-icons/bs";
import TechryptChatbot from "../TechryptChatbot/TechryptChatbot";

const MessageSidebar = () => {
  const [isChatbotOpen, setIsChatbotOpen] = useState(false);

  const toggleChatbot = () => {
    setIsChatbotOpen(!isChatbotOpen);
  };

  return (
    <>
      <BsRobot
        title="AI Assistant"
        className="fixed bottom-8 right-8 z-[999] text-primary text-5xl bg-black rounded-full p-1 hover:bg-primary hover:text-blue-700 duration-300 transition-all cursor-pointer"
        onClick={toggleChatbot}
      />

      {isChatbotOpen && (
        <TechryptChatbot
          isOpen={isChatbotOpen}
          onClose={() => setIsChatbotOpen(false)}
        />
      )}
    </>
  );
};

export default MessageSidebar;
