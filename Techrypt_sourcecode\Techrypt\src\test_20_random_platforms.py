#!/usr/bin/env python3
"""
Test 20 Random Websites and Services - Super-Intelligent Platform Detection
"""

import requests
import json
import time

def test_20_random_platforms():
    """Test 20 random websites/services to verify intelligent platform detection"""
    
    base_url = "http://localhost:5000"
    
    # 20 Random websites, platforms, and services
    test_cases = [
        # E-commerce Platforms
        {"input": "bigcommerce", "category": "E-commerce", "expected": "e-commerce platform"},
        {"input": "prestashop", "category": "E-commerce", "expected": "online store"},
        {"input": "opencart", "category": "E-commerce", "expected": "e-commerce"},
        {"input": "magento", "category": "E-commerce", "expected": "e-commerce"},
        
        # Social Media Platforms
        {"input": "tiktok business", "category": "Social Media", "expected": "social media"},
        {"input": "linkedin business", "category": "Social Media", "expected": "social media"},
        {"input": "youtube channel", "category": "Social Media", "expected": "social media"},
        {"input": "pinterest business", "category": "Social Media", "expected": "social media"},
        
        # CMS/Website Builders
        {"input": "webflow", "category": "CMS", "expected": "website"},
        {"input": "squarespace", "category": "CMS", "expected": "website"},
        {"input": "wix", "category": "CMS", "expected": "website"},
        {"input": "ghost", "category": "CMS", "expected": "website"},
        
        # Payment/Financial
        {"input": "square", "category": "Payment", "expected": "payment"},
        {"input": "razorpay", "category": "Payment", "expected": "payment"},
        {"input": "klarna", "category": "Payment", "expected": "payment"},
        
        # Cloud/Tech Services
        {"input": "aws", "category": "Cloud", "expected": "cloud"},
        {"input": "digitalocean", "category": "Cloud", "expected": "cloud"},
        {"input": "heroku", "category": "Cloud", "expected": "hosting"},
        
        # Business Tools
        {"input": "hubspot", "category": "Business", "expected": "crm"},
        {"input": "mailchimp", "category": "Business", "expected": "email marketing"}
    ]
    
    print("🧠 Testing 20 Random Websites & Services")
    print("=" * 80)
    print("Testing super-intelligent platform detection for ANY business platform...")
    print("=" * 80)
    
    success_count = 0
    platform_specific_count = 0
    total_count = len(test_cases)
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/{total_count}:")
        print(f"Platform: '{test_case['input']}'")
        print(f"Category: {test_case['category']}")
        print(f"Expected: Contains '{test_case['expected']}'")
        
        try:
            payload = {"message": test_case['input'], "name": ""}
            response = requests.post(f"{base_url}/chat", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '')
                
                print(f"Response: {ai_response[:150]}...")
                
                # Check if correct category is detected
                response_lower = ai_response.lower()
                category_detected = False
                platform_specific = False
                
                # Check for category-specific keywords
                if test_case['expected'] in response_lower:
                    category_detected = True
                
                # Check if response is platform-specific (mentions the platform name)
                if test_case['input'].lower() in response_lower:
                    platform_specific = True
                
                # Check for generic vs specific responses
                generic_phrases = [
                    "i'm here to help you with techrypt.io's digital marketing services",
                    "what type of business do you have",
                    "thank you for your message"
                ]
                
                is_generic = any(phrase in response_lower for phrase in generic_phrases)
                
                # Scoring
                if category_detected and platform_specific and not is_generic:
                    print("✅ EXCELLENT - Platform-specific intelligent response")
                    success_count += 1
                    platform_specific_count += 1
                    score = "EXCELLENT"
                elif category_detected and not is_generic:
                    print("✅ GOOD - Category detected, intelligent response")
                    success_count += 1
                    score = "GOOD"
                elif platform_specific and not is_generic:
                    print("⚠️ PARTIAL - Platform mentioned but category unclear")
                    score = "PARTIAL"
                elif not is_generic:
                    print("⚠️ BASIC - Non-generic response but not specific")
                    score = "BASIC"
                else:
                    print("❌ GENERIC - Generic response, needs improvement")
                    score = "GENERIC"
                
                results.append({
                    "platform": test_case['input'],
                    "category": test_case['category'],
                    "response": ai_response[:100] + "...",
                    "score": score,
                    "platform_specific": platform_specific,
                    "category_detected": category_detected
                })
                    
            else:
                print(f"❌ ERROR - HTTP {response.status_code}")
                results.append({
                    "platform": test_case['input'],
                    "category": test_case['category'],
                    "response": f"HTTP Error {response.status_code}",
                    "score": "ERROR",
                    "platform_specific": False,
                    "category_detected": False
                })
                
        except Exception as e:
            print(f"❌ ERROR - {e}")
            results.append({
                "platform": test_case['input'],
                "category": test_case['category'],
                "response": f"Exception: {e}",
                "score": "ERROR",
                "platform_specific": False,
                "category_detected": False
            })
        
        print("-" * 80)
        time.sleep(1)  # Small delay to avoid overwhelming the server
    
    # Final Results Summary
    print(f"\n🏆 FINAL RESULTS SUMMARY:")
    print("=" * 80)
    print(f"✅ Successful detections: {success_count}/{total_count}")
    print(f"🎯 Platform-specific responses: {platform_specific_count}/{total_count}")
    print(f"📊 Overall success rate: {(success_count/total_count)*100:.1f}%")
    print(f"🔥 Platform-specific rate: {(platform_specific_count/total_count)*100:.1f}%")
    
    # Category breakdown
    print(f"\n📋 CATEGORY BREAKDOWN:")
    categories = {}
    for result in results:
        cat = result['category']
        if cat not in categories:
            categories[cat] = {'total': 0, 'success': 0, 'platform_specific': 0}
        categories[cat]['total'] += 1
        if result['score'] in ['EXCELLENT', 'GOOD']:
            categories[cat]['success'] += 1
        if result['platform_specific']:
            categories[cat]['platform_specific'] += 1
    
    for cat, stats in categories.items():
        success_rate = (stats['success']/stats['total'])*100
        specific_rate = (stats['platform_specific']/stats['total'])*100
        print(f"  {cat}: {stats['success']}/{stats['total']} success ({success_rate:.1f}%), {stats['platform_specific']}/{stats['total']} platform-specific ({specific_rate:.1f}%)")
    
    # Detailed results
    print(f"\n📝 DETAILED RESULTS:")
    print("-" * 80)
    for result in results:
        status_emoji = {
            'EXCELLENT': '🌟',
            'GOOD': '✅', 
            'PARTIAL': '⚠️',
            'BASIC': '🔶',
            'GENERIC': '❌',
            'ERROR': '💥'
        }.get(result['score'], '❓')
        
        print(f"{status_emoji} {result['platform']} ({result['category']}) - {result['score']}")
        print(f"   Response: {result['response']}")
        print()
    
    # Overall assessment
    if success_count >= total_count * 0.8:  # 80% success rate
        print("🎉 EXCELLENT! Super-intelligent platform detection working amazingly!")
        if platform_specific_count >= total_count * 0.6:  # 60% platform-specific
            print("🚀 OUTSTANDING! Most responses are platform-specific and intelligent!")
    elif success_count >= total_count * 0.6:  # 60% success rate
        print("⚠️ GOOD but needs improvement for better platform intelligence")
    else:
        print("❌ POOR - Major improvements needed for platform detection")
    
    return results

if __name__ == "__main__":
    results = test_20_random_platforms()
    
    # Save results to file
    with open('platform_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    print(f"\n💾 Results saved to platform_test_results.json")
