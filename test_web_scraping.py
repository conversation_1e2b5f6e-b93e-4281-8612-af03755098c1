#!/usr/bin/env python3
"""
Test Web Scraping Service Detection
"""

import requests
import json

def test_web_scraping():
    """Test that web scraping is correctly detected as a service, not a business"""
    
    base_url = "http://localhost:5000"
    
    print("🕷️ Testing Web Scraping Service Detection")
    print("=" * 50)
    print("✅ Should detect as WEB SCRAPING SERVICE")
    print("❌ Should NOT detect as 'Web' business platform")
    print("=" * 50)
    
    # Test cases for web scraping
    test_cases = [
        {"input": "web scraping", "description": "Web scraping service"},
        {"input": "web scrapping", "description": "Web scrapping (misspelled)"},
        {"input": "data scraping", "description": "Data scraping service"},
        {"input": "data extraction", "description": "Data extraction service"},
        {"input": "web crawling", "description": "Web crawling service"},
        {"input": "scraping services", "description": "Scraping services"},
        {"input": "data mining", "description": "Data mining service"},
        {"input": "web data extraction", "description": "Web data extraction"},
        {"input": "automated data collection", "description": "Automated data collection"},
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/{len(test_cases)}: '{test_case['input']}'")
        print(f"   Expected: {test_case['description']}")
        
        try:
            # Send request without name to test clean detection
            payload = {"message": test_case['input'], "name": ""}
            response = requests.post(f"{base_url}/chat", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '')
                
                print(f"   Response: {ai_response[:150]}...")
                
                # Check if it's correctly detected as web scraping service
                scraping_indicators = [
                    'web scraping', 'data extraction', 'scraping solutions', 
                    'data collection', 'website development team', 'scraping services'
                ]
                
                # Check if it's incorrectly detected as business platform
                business_indicators = [
                    'web e-commerce business', 'web business', 'web platform',
                    'based on my research, web is', 'working with web'
                ]
                
                scraping_detected = any(indicator.lower() in ai_response.lower() for indicator in scraping_indicators)
                business_detected = any(indicator.lower() in ai_response.lower() for indicator in business_indicators)
                
                if scraping_detected and not business_detected:
                    print("   ✅ CORRECT - Detected as web scraping SERVICE")
                elif business_detected:
                    print("   ❌ WRONG - Detected as business platform instead of service")
                elif 'website development' in ai_response.lower() and not business_detected:
                    print("   ⚠️ PARTIAL - Detected as website service (acceptable)")
                else:
                    print("   ❓ UNCLEAR - Response unclear")
                
            else:
                print(f"   ❌ ERROR - HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ ERROR - {e}")
        
        print("-" * 50)
    
    print(f"\n🎯 WEB SCRAPING TEST COMPLETE!")
    print("✅ Correct = Detected as web scraping service")
    print("❌ Wrong = Detected as business platform")
    print("⚠️ Partial = Detected as website service (acceptable)")
    print("❓ Unclear = Response unclear")

if __name__ == "__main__":
    test_web_scraping()
