[{"input": "kababjis", "type": "Restaurant", "response": "Great! I understand you're working with <PERSON><PERSON><PERSON><PERSON><PERSON>. Based on my research, Kababjis is a restaurant/food business.\n\nFor your kababjis restaurant business...", "score": "EXCELLENT", "input_mentioned": true, "keywords_found": ["restaurant", "food", "online ordering", "menu"], "web_search_used": true, "business_specific": true, "manual_response": false}, {"input": "burger king", "type": "Restaurant", "response": "Great! I understand you're working with <PERSON>. Based on my research, Burger is an e-commerce platform.\n\nFor your burger e-commerce business, we can h...", "score": "GENERIC", "input_mentioned": false, "keywords_found": [], "web_search_used": true, "business_specific": true, "manual_response": false}, {"input": "mc<PERSON><PERSON><PERSON>", "type": "Restaurant", "response": "Great! I understand you're working with <PERSON><PERSON><PERSON><PERSON><PERSON>. Based on my research, Mcdonalds is a restaurant/food business.\n\nFor your mcdonalds restaurant busin...", "score": "EXCELLENT", "input_mentioned": true, "keywords_found": ["restaurant", "online ordering", "menu"], "web_search_used": true, "business_specific": true, "manual_response": false}, {"input": "pizza hut", "type": "Restaurant", "response": "Great! I understand you're working with Pizza. Based on my research, Pizza is an e-commerce platform.\n\nFor your pizza e-commerce business, we can help...", "score": "PARTIAL", "input_mentioned": false, "keywords_found": ["pizza"], "web_search_used": true, "business_specific": true, "manual_response": false}, {"input": "amazon", "type": "E-commerce", "response": "Great! I understand you're working with Amazon. Based on my research, Amazon is an e-commerce platform.\n\nFor your amazon e-commerce business, we can h...", "score": "EXCELLENT", "input_mentioned": true, "keywords_found": ["e-commerce", "payment", "inventory"], "web_search_used": true, "business_specific": true, "manual_response": false}, {"input": "ebay", "type": "E-commerce", "response": "Exception: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))", "score": "ERROR", "input_mentioned": false, "keywords_found": [], "web_search_used": false, "business_specific": false, "manual_response": false}, {"input": "etsy", "type": "E-commerce", "response": "Great! I understand you're working with <PERSON><PERSON><PERSON>. Based on my research, Etsy is an e-commerce platform.\n\nFor your etsy e-commerce business, we can help wi...", "score": "EXCELLENT", "input_mentioned": true, "keywords_found": ["e-commerce", "payment"], "web_search_used": true, "business_specific": true, "manual_response": false}, {"input": "uber", "type": "Service", "response": "Great! I understand you're working with Uber. Based on my research, Uber is a service business.\n\nFor your uber service business, we can help with:\n\n1....", "score": "EXCELLENT", "input_mentioned": true, "keywords_found": ["service", "booking", "customer", "automation"], "web_search_used": true, "business_specific": true, "manual_response": false}, {"input": "airbnb", "type": "Service", "response": "Exception: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))", "score": "ERROR", "input_mentioned": false, "keywords_found": [], "web_search_used": false, "business_specific": false, "manual_response": false}, {"input": "I have a wood business", "type": "Business Type", "response": "Great! I understand you're working with Wood Business. Based on my research, Wood Business is an e-commerce platform.\n\nFor your wood business e-commer...", "score": "EXCELLENT", "input_mentioned": false, "keywords_found": ["wood business", "business"], "web_search_used": true, "business_specific": true, "manual_response": false}, {"input": "I have a furniture business", "type": "Business Type", "response": "Great! I understand you're working with Furniture Business. Based on my research, Furniture Business is an e-commerce platform.\n\nFor your furniture bu...", "score": "EXCELLENT", "input_mentioned": false, "keywords_found": ["furniture business", "business"], "web_search_used": true, "business_specific": true, "manual_response": false}, {"input": "my construction business", "type": "Business Type", "response": "Great! I understand you're working with Construction Business. Based on my research, Construction Business is an e-commerce platform.\n\nFor your constr...", "score": "EXCELLENT", "input_mentioned": false, "keywords_found": ["construction business", "business"], "web_search_used": true, "business_specific": true, "manual_response": false}, {"input": "shopify", "type": "Known Platform", "response": "Great! I understand you're working with Shopify. Based on my research, Shopify is an e-commerce platform.\n\nFor your shopify e-commerce business, we ca...", "score": "EXCELLENT", "input_mentioned": true, "keywords_found": ["shopify", "e-commerce"], "web_search_used": true, "business_specific": true, "manual_response": false}, {"input": "wordpress", "type": "Known Platform", "response": "Great! I understand you're working with Wordpress. Based on my research, Wordpress is an e-commerce platform.\n\nFor your wordpress e-commerce business,...", "score": "EXCELLENT", "input_mentioned": true, "keywords_found": ["wordpress", "website"], "web_search_used": true, "business_specific": true, "manual_response": false}, {"input": "stripe", "type": "Known Platform", "response": "Great! I understand you're working with <PERSON><PERSON>.\n\nFor your stripe business, we can help with:\n\n1. Website Development\n2. Social Media Marketing\n3. Bran...", "score": "BASIC", "input_mentioned": true, "keywords_found": ["stripe", "payment", "integration"], "web_search_used": true, "business_specific": false, "manual_response": false}]