#!/usr/bin/env python3
"""
🔍 DOWNLOAD MONITOR
Monitors LLM download progress and automatically triggers testing when complete
"""

import time
import subprocess
import threading
import requests
from datetime import datetime

class DownloadMonitor:
    def __init__(self):
        self.monitoring = True
        self.download_complete = False
        self.server_url = "http://localhost:5000"
        
    def check_server_status(self):
        """Check if server is responding"""
        try:
            response = requests.get(f"{self.server_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def check_llm_models_loaded(self):
        """Check if LLM models are successfully loaded"""
        try:
            # Send a test message to see if LLM is working
            response = requests.post(
                f"{self.server_url}/chat",
                json={'message': 'test llm functionality', 'user_name': 'monitor'},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '')
                
                # Check response quality and length (LLM typically gives longer responses)
                if len(ai_response) > 100 and 'techrypt' in ai_response.lower():
                    print("✅ LLM models appear to be working!")
                    return True
                else:
                    print("⏳ Still using CSV mode...")
                    return False
            return False
        except Exception as e:
            print(f"⚠️ Server check error: {e}")
            return False
    
    def check_download_progress(self):
        """Check download progress from server logs"""
        try:
            # This is a simplified check - in practice you'd parse server logs
            llm_working = self.check_llm_models_loaded()
            return llm_working
        except:
            return False
    
    def monitor_downloads(self):
        """Main monitoring loop"""
        print("🔍 DOWNLOAD MONITOR STARTED")
        print(f"⏰ Started at: {datetime.now()}")
        print("=" * 60)
        
        check_count = 0
        
        while self.monitoring and not self.download_complete:
            check_count += 1
            current_time = datetime.now().strftime("%H:%M:%S")
            
            print(f"\n🔄 Check #{check_count} at {current_time}")
            
            # Check server status
            if not self.check_server_status():
                print("❌ Server not responding - waiting...")
                time.sleep(30)
                continue
            
            print("✅ Server is responding")
            
            # Check if downloads are complete
            if self.check_download_progress():
                print("🎉 LLM MODELS DETECTED AS WORKING!")
                self.download_complete = True
                break
            
            print("⏳ Downloads still in progress...")
            print("💡 Current status: Enhanced CSV mode active")
            
            # Wait before next check
            print("⏰ Next check in 60 seconds...")
            time.sleep(60)
        
        if self.download_complete:
            print("\n🚀 DOWNLOADS COMPLETE! Starting test pipeline...")
            self.start_test_pipeline()
        else:
            print("\n⏹️ Monitoring stopped")
    
    def start_test_pipeline(self):
        """Start the comprehensive test and train pipeline"""
        print("\n🎯 LAUNCHING COMPREHENSIVE TEST & TRAIN PIPELINE")
        print("=" * 80)
        
        try:
            # Run the test pipeline
            result = subprocess.run([
                'python', 'auto_test_train_pipeline.py'
            ], capture_output=True, text=True, timeout=1800)  # 30 minute timeout
            
            if result.returncode == 0:
                print("✅ Test pipeline completed successfully!")
                print(result.stdout)
            else:
                print("❌ Test pipeline failed!")
                print(result.stderr)
                
        except subprocess.TimeoutExpired:
            print("⏰ Test pipeline timed out (30 minutes)")
        except Exception as e:
            print(f"❌ Error running test pipeline: {e}")
    
    def stop_monitoring(self):
        """Stop the monitoring process"""
        self.monitoring = False

def main():
    """Main function"""
    monitor = DownloadMonitor()
    
    try:
        monitor.monitor_downloads()
    except KeyboardInterrupt:
        print("\n⏹️ Monitoring stopped by user")
        monitor.stop_monitoring()
    except Exception as e:
        print(f"\n❌ Monitoring error: {e}")

if __name__ == "__main__":
    main()
