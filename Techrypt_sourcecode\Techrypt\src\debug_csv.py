#!/usr/bin/env python3
"""
Debug CSV parsing
"""

import os
import re

def debug_csv():
    csv_path = os.path.join(os.path.dirname(__file__), '..', 'data.csv')
    
    with open(csv_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()
        
        print(f"Total lines: {len(lines)}")
        print(f"Header: {lines[0].strip()}")
        
        # Test first few data lines
        for i in range(1, min(6, len(lines))):
            line = lines[i].strip()
            print(f"\nLine {i}: {line[:100]}...")
            
            # Try different parsing methods
            
            # Method 1: Simple split
            simple_split = line.split(',')
            print(f"Simple split ({len(simple_split)} parts): {[p[:30] for p in simple_split[:4]]}")
            
            # Method 2: Regex split
            regex_split = re.split(r',(?=(?:[^"]*"[^"]*")*[^"]*$)', line)
            print(f"Regex split ({len(regex_split)} parts): {[p[:30] for p in regex_split[:4]]}")
            
            # Method 3: Manual parsing
            parts = []
            current = ""
            in_quotes = False
            
            for char in line:
                if char == '"':
                    in_quotes = not in_quotes
                elif char == ',' and not in_quotes:
                    parts.append(current.strip('"'))
                    current = ""
                    continue
                current += char
            parts.append(current.strip('"'))
            
            print(f"Manual parse ({len(parts)} parts): {[p[:30] for p in parts[:4]]}")

if __name__ == "__main__":
    debug_csv()
