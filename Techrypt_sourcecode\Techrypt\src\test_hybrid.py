#!/usr/bin/env python3
"""
Test Hybrid Approach: LLM + Hardcoded
"""

import requests
import json

def test_hybrid():
    """Test hybrid approach with different types of inputs"""
    
    base_url = "http://localhost:5000"
    
    test_cases = [
        {
            "input": "services",
            "expected_type": "hardcoded",
            "description": "Should use hardcoded structured service list"
        },
        {
            "input": "daraz",
            "expected_type": "hardcoded", 
            "description": "Should use hardcoded business platform detection"
        },
        {
            "input": "hello",
            "expected_type": "llm",
            "description": "Should use LLM for natural greeting"
        },
        {
            "input": "how are you",
            "expected_type": "llm",
            "description": "Should use LLM for conversation"
        },
        {
            "input": "tell me about your company",
            "expected_type": "llm",
            "description": "Should use LLM for general questions"
        }
    ]
    
    print("🔄 Testing Hybrid Approach: LLM + Hardcoded")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/{len(test_cases)}:")
        print(f"Input: '{test_case['input']}'")
        print(f"Expected: {test_case['expected_type']} response")
        print(f"Description: {test_case['description']}")
        
        try:
            payload = {"message": test_case['input'], "name": ""}
            response = requests.post(f"{base_url}/chat", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '')
                
                print(f"Response: {ai_response[:100]}...")
                
                # Check response type
                is_structured = ('1.' in ai_response and '2.' in ai_response)
                is_business_platform = ('daraz' in ai_response.lower() and 'store' in ai_response.lower())
                
                if test_case['expected_type'] == 'hardcoded':
                    if is_structured or is_business_platform:
                        print("✅ CORRECT - Using hardcoded structured response")
                    else:
                        print("❌ WRONG - Should be hardcoded but seems like LLM")
                else:  # LLM expected
                    if not is_structured and not is_business_platform:
                        print("✅ CORRECT - Using LLM conversational response")
                    else:
                        print("❌ WRONG - Should be LLM but seems hardcoded")
                        
            else:
                print(f"❌ ERROR - HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ ERROR - {e}")
        
        print("-" * 60)

if __name__ == "__main__":
    test_hybrid()
