#!/usr/bin/env python3
"""
Test Enhanced Web Search + Business Intelligence System
"""

import requests
import json
import time

def test_enhanced_web_search():
    """Test the enhanced web search + business intelligence system"""
    
    base_url = "http://localhost:5000"
    
    # Test cases for different business types
    test_cases = [
        # Restaurant Businesses (should use web search)
        {"input": "kababjis", "type": "Restaurant", "expected": ["restaurant", "food", "online ordering", "menu"]},
        {"input": "burger king", "type": "Restaurant", "expected": ["restaurant", "fast food", "online ordering", "menu"]},
        {"input": "mcdonalds", "type": "Restaurant", "expected": ["restaurant", "fast food", "online ordering", "menu"]},
        {"input": "pizza hut", "type": "Restaurant", "expected": ["restaurant", "pizza", "online ordering", "delivery"]},
        
        # E-commerce Businesses (should use web search)
        {"input": "amazon", "type": "E-commerce", "expected": ["e-commerce", "online store", "payment", "inventory"]},
        {"input": "ebay", "type": "E-commerce", "expected": ["e-commerce", "marketplace", "payment", "selling"]},
        {"input": "etsy", "type": "E-commerce", "expected": ["e-commerce", "handmade", "online store", "payment"]},
        
        # Service Businesses (should use web search)
        {"input": "uber", "type": "Service", "expected": ["service", "booking", "customer", "automation"]},
        {"input": "airbnb", "type": "Service", "expected": ["service", "booking", "rental", "customer"]},
        
        # Business Types (should extract properly)
        {"input": "I have a wood business", "type": "Business Type", "expected": ["wood business", "business"]},
        {"input": "I have a furniture business", "type": "Business Type", "expected": ["furniture business", "business"]},
        {"input": "my construction business", "type": "Business Type", "expected": ["construction business", "business"]},
        
        # Known Platforms (should use manual responses)
        {"input": "shopify", "type": "Known Platform", "expected": ["shopify", "store", "e-commerce"]},
        {"input": "wordpress", "type": "Known Platform", "expected": ["wordpress", "website", "cms"]},
        {"input": "stripe", "type": "Known Platform", "expected": ["stripe", "payment", "integration"]},
    ]
    
    print("🌐 Testing Enhanced Web Search + Business Intelligence")
    print("=" * 80)
    print("Testing web search for unknown businesses + manual responses for known platforms...")
    print("=" * 80)
    
    web_search_count = 0
    business_specific_count = 0
    manual_response_count = 0
    total_count = len(test_cases)
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/{total_count}:")
        print(f"Input: '{test_case['input']}'")
        print(f"Expected Type: {test_case['type']}")
        print(f"Expected Keywords: {test_case['expected']}")
        
        try:
            payload = {"message": test_case['input'], "name": ""}
            response = requests.post(f"{base_url}/chat", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '')
                
                print(f"Response: {ai_response[:200]}...")
                
                # Check response characteristics
                response_lower = ai_response.lower()
                input_name = test_case['input'].lower()
                
                # Check if input is mentioned in response
                input_mentioned = input_name in response_lower
                
                # Check for expected keywords
                keywords_found = []
                for keyword in test_case['expected']:
                    if keyword in response_lower:
                        keywords_found.append(keyword)
                
                # Check for web search indicators
                web_search_indicators = [
                    "based on my research",
                    "i understand you're working with",
                    "great! i understand",
                    f"{input_name} is a",
                    f"{input_name} is an"
                ]
                web_search_used = any(indicator in response_lower for indicator in web_search_indicators)
                
                # Check for business-specific features
                business_features = [
                    "restaurant business", "e-commerce business", "service business",
                    "online ordering", "menu display", "reservation system",
                    "payment processing", "inventory management", "booking system"
                ]
                business_specific = any(feature in response_lower for feature in business_features)
                
                # Check for manual response patterns
                manual_patterns = [
                    "for your .* store, we can help with:",
                    "for your .* website, we can help with:",
                    "for your .* integration, we can help with:"
                ]
                manual_response = any(pattern in response_lower for pattern in manual_patterns)
                
                # Scoring
                if web_search_used and business_specific and len(keywords_found) >= 2:
                    print("🌟 EXCELLENT - Web search + business-specific response")
                    web_search_count += 1
                    business_specific_count += 1
                    score = "EXCELLENT"
                elif manual_response and input_mentioned and len(keywords_found) >= 1:
                    print("✅ GOOD - Manual response working correctly")
                    manual_response_count += 1
                    business_specific_count += 1
                    score = "GOOD"
                elif business_specific and len(keywords_found) >= 1:
                    print("⚠️ PARTIAL - Business-specific but unclear source")
                    business_specific_count += 1
                    score = "PARTIAL"
                elif input_mentioned:
                    print("🔶 BASIC - Input mentioned but not business-specific")
                    score = "BASIC"
                else:
                    print("❌ GENERIC - Generic response")
                    score = "GENERIC"
                
                results.append({
                    "input": test_case['input'],
                    "type": test_case['type'],
                    "response": ai_response[:150] + "...",
                    "score": score,
                    "input_mentioned": input_mentioned,
                    "keywords_found": keywords_found,
                    "web_search_used": web_search_used,
                    "business_specific": business_specific,
                    "manual_response": manual_response
                })
                    
            else:
                print(f"❌ ERROR - HTTP {response.status_code}")
                results.append({
                    "input": test_case['input'],
                    "type": test_case['type'],
                    "response": f"HTTP Error {response.status_code}",
                    "score": "ERROR",
                    "input_mentioned": False,
                    "keywords_found": [],
                    "web_search_used": False,
                    "business_specific": False,
                    "manual_response": False
                })
                
        except Exception as e:
            print(f"❌ ERROR - {e}")
            results.append({
                "input": test_case['input'],
                "type": test_case['type'],
                "response": f"Exception: {e}",
                "score": "ERROR",
                "input_mentioned": False,
                "keywords_found": [],
                "web_search_used": False,
                "business_specific": False,
                "manual_response": False
            })
        
        print("-" * 80)
        time.sleep(1)  # Small delay to avoid overwhelming the system
    
    # Final Results Summary
    print(f"\n🏆 ENHANCED WEB SEARCH RESULTS:")
    print("=" * 80)
    print(f"🌐 Web search responses: {web_search_count}/{total_count}")
    print(f"📋 Manual responses: {manual_response_count}/{total_count}")
    print(f"🎯 Business-specific responses: {business_specific_count}/{total_count}")
    print(f"📊 Web search success rate: {(web_search_count/total_count)*100:.1f}%")
    print(f"🔧 Manual response rate: {(manual_response_count/total_count)*100:.1f}%")
    print(f"🔥 Business intelligence rate: {(business_specific_count/total_count)*100:.1f}%")
    
    # Type breakdown
    print(f"\n📋 BUSINESS TYPE BREAKDOWN:")
    types = {}
    for result in results:
        btype = result['type']
        if btype not in types:
            types[btype] = {'total': 0, 'web_search': 0, 'manual': 0, 'business_specific': 0}
        types[btype]['total'] += 1
        if result['web_search_used']:
            types[btype]['web_search'] += 1
        if result['manual_response']:
            types[btype]['manual'] += 1
        if result['business_specific']:
            types[btype]['business_specific'] += 1
    
    for btype, stats in types.items():
        web_rate = (stats['web_search']/stats['total'])*100
        manual_rate = (stats['manual']/stats['total'])*100
        business_rate = (stats['business_specific']/stats['total'])*100
        print(f"  {btype}: Web {stats['web_search']}/{stats['total']} ({web_rate:.1f}%), Manual {stats['manual']}/{stats['total']} ({manual_rate:.1f}%), Business-specific {stats['business_specific']}/{stats['total']} ({business_rate:.1f}%)")
    
    # Overall assessment
    total_intelligent = web_search_count + manual_response_count
    if total_intelligent >= total_count * 0.8:  # 80% intelligent responses
        print("🎉 EXCELLENT! Enhanced web search + manual responses working perfectly!")
        if business_specific_count >= total_count * 0.7:  # 70% business-specific
            print("🚀 OUTSTANDING! Most responses are business-specific!")
    elif total_intelligent >= total_count * 0.6:  # 60% intelligent responses
        print("⚠️ GOOD but system could be more intelligent")
    else:
        print("❌ POOR - Major improvements needed")
    
    return results

if __name__ == "__main__":
    results = test_enhanced_web_search()
    
    # Save results to file
    with open('enhanced_web_search_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    print(f"\n💾 Results saved to enhanced_web_search_results.json")
