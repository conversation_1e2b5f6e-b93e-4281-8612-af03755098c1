#!/usr/bin/env python3
"""
Quick test for mixed scenarios (business + service combinations)
"""

import requests
import json
import time

def test_mixed_scenarios():
    """Test mixed business + service scenarios"""
    
    base_url = "http://localhost:5000"
    
    print("🔍 MIXED SCENARIOS TEST")
    print("=" * 50)
    
    mixed_tests = [
        "I have a restaurant and need a website",
        "My clothing store needs social media marketing", 
        "Bakery business looking for Instagram growth",
        "Dental clinic needs appointment booking system",
        "Gym business wants payment gateway"
    ]
    
    for i, test_input in enumerate(mixed_tests, 1):
        print(f"\n🧪 Test {i}: '{test_input}'")
        
        try:
            payload = {"message": test_input, "name": ""}
            response = requests.post(f"{base_url}/chat", json=payload, timeout=15)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '')
                
                print(f"✅ Response received ({len(ai_response)} chars)")
                print(f"📝 Response: {ai_response[:200]}...")
                
                # Check if it's a good mixed scenario response
                response_lower = ai_response.lower()
                business_indicators = ['for your', 'your business', 'restaurant business', 'retail business', 'salon business']
                service_indicators = ['website', 'social media', 'branding', 'chatbot', 'payment', 'booking', 'automation']
                
                has_business = any(indicator in response_lower for indicator in business_indicators)
                has_service = any(indicator in response_lower for indicator in service_indicators)
                
                if has_business and has_service and len(ai_response) > 100:
                    print("✅ EXCELLENT: Perfect business + service combination response!")
                else:
                    print("⚠️ BASIC: Generic response, could be more specific")
                    
            else:
                print(f"❌ ERROR: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ ERROR: {e}")
        
        time.sleep(2)  # Prevent server overload
    
    print(f"\n🎯 Mixed scenarios test complete!")

if __name__ == "__main__":
    test_mixed_scenarios()
