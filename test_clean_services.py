#!/usr/bin/env python3
"""
Test Clean Service List Formatting
"""

import requests
import json

def test_clean_service_list():
    """Test that service lists are displayed cleanly line by line"""
    
    base_url = "http://localhost:5000"
    
    print("📋 Testing Clean Service List Formatting")
    print("=" * 50)
    print("✅ Services should be listed cleanly line by line")
    print("❌ No cramped formatting or long descriptions")
    print("=" * 50)
    
    # Test cases that should show service lists
    test_cases = [
        {"input": "services", "description": "Direct service request"},
        {"input": "what do you offer", "description": "Service inquiry"},
        {"input": "list services", "description": "List services request"},
        {"input": "your services", "description": "Your services request"},
        {"input": "copywriting", "description": "Copywriting service (should be clean)"},
        {"input": "I have a restaurant", "description": "Restaurant business (should show clean list)"},
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/{len(test_cases)}: '{test_case['input']}'")
        print(f"   Expected: {test_case['description']}")
        
        try:
            payload = {"message": test_case['input'], "name": "<PERSON>"}
            response = requests.post(f"{base_url}/chat", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '')
                
                print(f"   Response:")
                print(f"   {ai_response}")
                
                # Check formatting quality
                lines = ai_response.split('\n')
                service_lines = [line for line in lines if line.strip() and any(char.isdigit() for char in line[:3])]
                
                if len(service_lines) >= 6:  # Should have 6 services
                    # Check if lines are clean (not too long)
                    clean_lines = all(len(line.strip()) < 80 for line in service_lines)
                    # Check if lines are properly numbered
                    numbered_lines = all(line.strip().startswith(str(i+1)) for i, line in enumerate(service_lines[:6]))
                    
                    if clean_lines and numbered_lines:
                        print("   ✅ CLEAN - Services listed cleanly line by line")
                    elif numbered_lines:
                        print("   ⚠️ PARTIAL - Numbered correctly but some lines too long")
                    else:
                        print("   ❌ MESSY - Poor formatting")
                else:
                    print("   ❓ NO LIST - No service list detected")
                
            else:
                print(f"   ❌ ERROR - HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ ERROR - {e}")
        
        print("-" * 50)
    
    print(f"\n🎯 FORMATTING TEST COMPLETE!")
    print("✅ Clean formatting = Services listed line by line without clutter")
    print("⚠️ Partial = Correct structure but some formatting issues")
    print("❌ Messy = Poor formatting or cramped text")

if __name__ == "__main__":
    test_clean_service_list()
