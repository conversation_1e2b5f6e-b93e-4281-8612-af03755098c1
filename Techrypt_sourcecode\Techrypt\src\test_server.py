#!/usr/bin/env python3
"""
🧪 TEST SERVER FOR LLM TESTING
Uses only cached models, no downloads
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import logging
import os
import sys

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Global variables
llm_model = None
llm_tokenizer = None
current_model = None

def initialize_llm():
    """Initialize LLM with cached models only"""
    global llm_model, llm_tokenizer, current_model
    
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        import torch
        
        # Models to try (cached only)
        models_to_try = [
            'microsoft/DialoGPT-medium',
            'microsoft/DialoGPT-small',
            'distilgpt2',
            'gpt2'
        ]
        
        for model_name in models_to_try:
            try:
                logger.info(f"🔍 Checking cached model: {model_name}")
                
                # Try to load from cache only
                tokenizer = AutoTokenizer.from_pretrained(
                    model_name, 
                    local_files_only=True
                )
                model = AutoModelForCausalLM.from_pretrained(
                    model_name,
                    local_files_only=True,
                    torch_dtype=torch.float32,
                    low_cpu_mem_usage=True
                )
                
                # Success!
                llm_model = model
                llm_tokenizer = tokenizer
                current_model = model_name
                
                logger.info(f"✅ LLM loaded from cache: {model_name}")
                return True
                
            except Exception as e:
                logger.warning(f"❌ {model_name} not in cache: {e}")
                continue
        
        logger.warning("⚠️ No LLM models found in cache")
        return False
        
    except Exception as e:
        logger.error(f"❌ LLM initialization failed: {e}")
        return False

def generate_llm_response(message, user_name="User"):
    """Generate response using LLM"""
    global llm_model, llm_tokenizer, current_model
    
    if not llm_model or not llm_tokenizer:
        return "I apologize, but the LLM model is not available. Please try again later."
    
    try:
        from transformers import pipeline
        
        # Create conversation context
        context = f"User {user_name}: {message}\nTechrypt Assistant:"
        
        # Generate response
        generator = pipeline(
            'text-generation',
            model=llm_model,
            tokenizer=llm_tokenizer,
            max_length=len(context.split()) + 50,
            do_sample=True,
            temperature=0.7,
            pad_token_id=llm_tokenizer.eos_token_id
        )
        
        result = generator(context, max_length=len(context.split()) + 50)
        generated_text = result[0]['generated_text']
        
        # Extract only the assistant's response
        if "Techrypt Assistant:" in generated_text:
            response = generated_text.split("Techrypt Assistant:")[-1].strip()
        else:
            response = generated_text[len(context):].strip()
        
        # Add Techrypt branding if not present
        if "techrypt" not in response.lower():
            response += " At Techrypt, we're here to help your business grow!"
        
        return response
        
    except Exception as e:
        logger.error(f"LLM generation error: {e}")
        return f"I'm having trouble generating a response right now. How can Techrypt help your business today?"

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    global current_model
    
    status = {
        "status": "healthy",
        "llm_model": current_model if current_model else "No LLM loaded",
        "mode": "LLM Testing Mode" if current_model else "No LLM Available"
    }
    
    return jsonify(status)

@app.route('/chat', methods=['POST'])
def chat():
    """Chat endpoint"""
    try:
        data = request.get_json()
        message = data.get('message', '').strip()
        user_name = data.get('user_name', 'User')
        
        if not message:
            return jsonify({
                "response": "Please provide a message.",
                "model": current_model,
                "mode": "error"
            })
        
        # Generate LLM response
        if current_model:
            response = generate_llm_response(message, user_name)
            mode = "LLM"
        else:
            response = "I apologize, but no LLM models are currently available for testing. Please ensure models are properly cached."
            mode = "error"
        
        return jsonify({
            "response": response,
            "model": current_model,
            "mode": mode,
            "user_name": user_name
        })
        
    except Exception as e:
        logger.error(f"Chat error: {e}")
        return jsonify({
            "response": "I encountered an error processing your request. Please try again.",
            "model": current_model,
            "mode": "error"
        }), 500

@app.route('/context', methods=['GET'])
def get_context():
    """Get current context"""
    return jsonify({
        "model": current_model,
        "mode": "LLM Testing" if current_model else "No LLM",
        "status": "ready" if current_model else "no_llm"
    })

@app.route('/reset', methods=['POST'])
def reset_context():
    """Reset conversation context"""
    return jsonify({
        "message": "Context reset (LLM models are stateless)",
        "model": current_model
    })

def main():
    """Main function"""
    print("🧪 TECHRYPT LLM TEST SERVER")
    print("=" * 50)
    
    # Initialize LLM
    llm_ready = initialize_llm()
    
    if llm_ready:
        print(f"✅ LLM Model Ready: {current_model}")
        print("🎯 Server will test LLM functionality")
    else:
        print("❌ No LLM models available in cache")
        print("💡 Server will report LLM unavailable")
    
    print("\n🚀 Starting Test Server...")
    print("📡 Server: http://localhost:5000")
    print("🔗 Health: http://localhost:5000/health")
    print("💬 Chat: POST http://localhost:5000/chat")
    print("=" * 50)
    
    # Start server
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=False  # No debug mode to avoid restarts
    )

if __name__ == "__main__":
    main()
