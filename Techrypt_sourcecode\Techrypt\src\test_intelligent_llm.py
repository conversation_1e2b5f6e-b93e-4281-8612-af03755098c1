#!/usr/bin/env python3
"""
Test Intelligent LLM Service Detection
"""

import requests
import json
import time

def test_intelligent_responses():
    """Test intelligent LLM responses with service detection"""
    
    base_url = "http://localhost:5000"
    
    # Test cases for intelligent service detection
    test_cases = [
        {
            "input": "daraz",
            "name": "",
            "expected": "should recognize Daraz as ecommerce platform and offer structured services"
        },
        {
            "input": "I have a Shopify store",
            "name": "<PERSON>",
            "expected": "should recognize Shopify and provide personalized ecommerce services"
        },
        {
            "input": "I need social media marketing",
            "name": "",
            "expected": "should detect SMM service and provide relevant response"
        },
        {
            "input": "help with my website",
            "name": "",
            "expected": "should detect website development service"
        },
        {
            "input": "I want a logo design",
            "name": "<PERSON>",
            "expected": "should detect branding service"
        },
        {
            "input": "automation for my business",
            "name": "",
            "expected": "should detect automation service"
        }
    ]
    
    print("🧠 Testing Intelligent LLM Service Detection")
    print("=" * 60)
    
    # Reset conversation first
    try:
        reset_response = requests.post(f"{base_url}/reset")
        if reset_response.status_code == 200:
            print("✅ Conversation reset successful")
        else:
            print("❌ Reset failed")
    except:
        print("❌ Could not reset conversation")
    
    passed = 0
    total = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/{total}:")
        print(f"Input: '{test_case['input']}' (name: '{test_case['name']}')")
        print(f"Expected: {test_case['expected']}")
        
        try:
            # Send request
            payload = {
                "message": test_case['input'],
                "name": test_case['name']
            }
            
            response = requests.post(f"{base_url}/chat", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '')
                
                print(f"Response: {ai_response[:200]}...")
                
                # Check if response is intelligent and structured
                is_intelligent = (
                    len(ai_response) > 50 and  # Substantial response
                    ('1.' in ai_response or '2.' in ai_response) and  # Structured format
                    ('Techrypt' in ai_response or 'service' in ai_response.lower())  # Relevant content
                )
                
                if is_intelligent:
                    print("✅ PASS - Intelligent structured response")
                    passed += 1
                else:
                    print("❌ FAIL - Response not intelligent enough")
                    
            else:
                print(f"❌ FAIL - HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ FAIL - Error: {e}")
        
        time.sleep(1)  # Brief pause between tests
    
    print("\n" + "=" * 60)
    print(f"📊 INTELLIGENT LLM TEST RESULTS")
    print("=" * 60)
    print(f"✅ Passed: {passed}/{total} ({passed/total*100:.1f}%)")
    print(f"❌ Failed: {total-passed}/{total}")
    
    if passed >= total * 0.8:  # 80% pass rate
        print("🎉 EXCELLENT! LLM is intelligent and detecting services correctly!")
    elif passed >= total * 0.6:  # 60% pass rate
        print("👍 GOOD! LLM is working but needs improvement")
    else:
        print("⚠️ NEEDS WORK! LLM intelligence needs enhancement")

if __name__ == "__main__":
    test_intelligent_responses()
