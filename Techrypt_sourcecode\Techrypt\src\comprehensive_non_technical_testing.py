#!/usr/bin/env python3
"""
🧪 COMPREHENSIVE NON-TECHNICAL USER TESTING SYSTEM
Tests the chatbot like a real non-technical user would interact with it
Runs continuous loops of testing and improvements until all test cases pass
"""

import requests
import time
import json
import random
from datetime import datetime

class NonTechnicalUserTester:
    def __init__(self, server_url="http://localhost:5000"):
        self.server_url = server_url
        self.test_results = []
        self.improvement_cycles = 0
        self.max_cycles = 10
        
    def simulate_real_user_conversations(self):
        """Simulate how real non-technical users would interact"""
        
        # 🎯 REAL USER SCENARIOS - Exactly like your pet shop example
        real_conversations = [
            {
                'scenario': 'Pet Shop Owner',
                'conversation': [
                    ('hello', 'greeting'),
                    ('i have pet shop how can you help me', 'business_introduction'),
                    ('how much ai chatbot costs', 'pricing_question'),
                    ('what business i have?', 'context_memory_test'),
                    ('smm', 'service_abbreviation'),
                    ('how will social media marketing help my pet shop', 'specific_business_help')
                ]
            },
            {
                'scenario': 'Restaurant Owner',
                'conversation': [
                    ('hi', 'greeting'),
                    ('i own a restaurant', 'business_introduction'),
                    ('i need website', 'service_request'),
                    ('what about social media', 'follow_up_service'),
                    ('how can instagram help my restaurant', 'specific_platform_help')
                ]
            },
            {
                'scenario': 'Bakery Owner with Typos',
                'conversation': [
                    ('hello', 'greeting'),
                    ('i have bread buisness', 'business_with_typo'),
                    ('i want ypur branding services', 'garbled_text_test'),
                    ('waht services do you offer', 'typo_question'),
                    ('hwo much does it cost', 'pricing_with_typo')
                ]
            },
            {
                'scenario': 'Confused New Business Owner',
                'conversation': [
                    ('hi there', 'greeting'),
                    ('i dont know what i need', 'confused_user'),
                    ('i have a small business', 'vague_business'),
                    ('can you help me grow', 'general_help_request'),
                    ('what is smm', 'acronym_question')
                ]
            },
            {
                'scenario': 'Multi-Service Requester',
                'conversation': [
                    ('good morning', 'greeting'),
                    ('i need website and chatbot', 'multiple_services'),
                    ('also branding', 'additional_service'),
                    ('how much for everything', 'package_pricing'),
                    ('can you do it all together', 'integration_question')
                ]
            }
        ]
        
        return real_conversations
    
    def test_conversation(self, conversation_data):
        """Test a complete conversation flow"""
        scenario = conversation_data['scenario']
        conversation = conversation_data['conversation']
        
        print(f"\n🎭 Testing Scenario: {scenario}")
        print("=" * 60)
        
        conversation_results = []
        context_memory_score = 0
        
        for i, (message, expected_type) in enumerate(conversation, 1):
            print(f"\n💬 Step {i}: \"{message}\"")
            print(f"Expected: {expected_type}")
            
            try:
                response = requests.post(
                    f"{self.server_url}/chat",
                    json={'message': message, 'user_name': 'testuser'},
                    timeout=10
                )
                
                if response.status_code == 200:
                    result = response.json()
                    ai_response = result.get('response', '')
                    
                    # Evaluate response quality
                    evaluation = self.evaluate_response_like_real_user(
                        message, ai_response, expected_type, scenario, i
                    )
                    
                    conversation_results.append({
                        'step': i,
                        'user_message': message,
                        'ai_response': ai_response,
                        'expected_type': expected_type,
                        'evaluation': evaluation
                    })
                    
                    # Display results
                    status = "✅ GOOD" if evaluation['helpful'] else "❌ BAD"
                    print(f"{status} Response: {ai_response[:100]}...")
                    
                    if evaluation['issues']:
                        print(f"🚨 Issues: {', '.join(evaluation['issues'])}")
                    
                    if evaluation['context_maintained']:
                        context_memory_score += 1
                        
                else:
                    print(f"❌ Server Error: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ Connection Error: {e}")
            
            time.sleep(1)  # Realistic user pause
        
        # Calculate conversation score
        total_steps = len(conversation)
        helpful_responses = sum(1 for r in conversation_results if r['evaluation']['helpful'])
        conversation_score = (helpful_responses / total_steps) * 100
        context_score = (context_memory_score / max(1, total_steps - 1)) * 100
        
        print(f"\n📊 Conversation Results:")
        print(f"Helpful Responses: {helpful_responses}/{total_steps} ({conversation_score:.1f}%)")
        print(f"Context Memory: {context_memory_score}/{max(1, total_steps - 1)} ({context_score:.1f}%)")
        
        return {
            'scenario': scenario,
            'conversation_score': conversation_score,
            'context_score': context_score,
            'results': conversation_results
        }
    
    def evaluate_response_like_real_user(self, user_message, ai_response, expected_type, scenario, step):
        """Evaluate response from a real user's perspective"""
        evaluation = {
            'helpful': False,
            'context_maintained': False,
            'issues': [],
            'strengths': []
        }
        
        response_lower = ai_response.lower()
        message_lower = user_message.lower()
        
        # 🎯 REAL USER EXPECTATIONS
        
        # 1. Basic helpfulness check
        helpful_indicators = ['help', 'can', 'will', 'techrypt', 'service', 'consultation']
        if any(indicator in response_lower for indicator in helpful_indicators):
            evaluation['helpful'] = True
            evaluation['strengths'].append("Offers help")
        else:
            evaluation['issues'].append("Doesn't seem helpful")
        
        # 2. Business context memory (critical for real users)
        if step > 1:  # After first message
            business_keywords = ['pet', 'restaurant', 'bakery', 'bread', 'food']
            if scenario == 'Pet Shop Owner' and 'pet' in response_lower:
                evaluation['context_maintained'] = True
                evaluation['strengths'].append("Remembers pet shop context")
            elif scenario == 'Restaurant Owner' and ('restaurant' in response_lower or 'food' in response_lower):
                evaluation['context_maintained'] = True
                evaluation['strengths'].append("Remembers restaurant context")
            elif scenario == 'Bakery Owner with Typos' and ('bread' in response_lower or 'bakery' in response_lower):
                evaluation['context_maintained'] = True
                evaluation['strengths'].append("Remembers bakery context")
            elif step > 2:  # Only flag as issue after step 2
                evaluation['issues'].append("Lost business context")
        
        # 3. Garbled text check (critical fix)
        garbled_patterns = ['ypur', 'buisness', 'waht', 'hwo', 'teh']
        if any(pattern in response_lower for pattern in garbled_patterns):
            evaluation['issues'].append("Contains garbled text")
            evaluation['helpful'] = False
        else:
            evaluation['strengths'].append("Clean text")
        
        # 4. Techrypt branding (business requirement)
        if 'techrypt' not in response_lower:
            evaluation['issues'].append("Missing Techrypt branding")
        else:
            evaluation['strengths'].append("Includes Techrypt branding")
        
        # 5. Consultation offer (business requirement)
        consultation_words = ['consultation', 'schedule', 'discuss', 'meeting']
        if not any(word in response_lower for word in consultation_words):
            evaluation['issues'].append("No consultation offer")
        else:
            evaluation['strengths'].append("Offers consultation")
        
        # 6. Specific response type checks
        if expected_type == 'greeting' and 'welcome' not in response_lower:
            evaluation['issues'].append("Greeting missing welcome")
        
        if expected_type == 'pricing_question' and 'consultation' not in response_lower:
            evaluation['issues'].append("Pricing question should suggest consultation")
        
        if expected_type == 'specific_business_help':
            if scenario == 'Pet Shop Owner' and not any(word in response_lower for word in ['pet', 'animal', 'cute', 'photo']):
                evaluation['issues'].append("Doesn't provide pet-specific help")
        
        return evaluation
    
    def run_comprehensive_testing_cycle(self):
        """Run one complete testing cycle"""
        print(f"\n🔄 TESTING CYCLE {self.improvement_cycles + 1}")
        print("=" * 80)
        
        conversations = self.simulate_real_user_conversations()
        cycle_results = []
        
        for conversation_data in conversations:
            result = self.test_conversation(conversation_data)
            cycle_results.append(result)
        
        # Calculate overall scores
        avg_conversation_score = sum(r['conversation_score'] for r in cycle_results) / len(cycle_results)
        avg_context_score = sum(r['context_score'] for r in cycle_results) / len(cycle_results)
        
        print(f"\n📈 CYCLE {self.improvement_cycles + 1} RESULTS:")
        print(f"Average Conversation Quality: {avg_conversation_score:.1f}%")
        print(f"Average Context Memory: {avg_context_score:.1f}%")
        
        # Determine if improvements are needed
        needs_improvement = avg_conversation_score < 80 or avg_context_score < 70
        
        if needs_improvement:
            print("⚠️ NEEDS IMPROVEMENT - Issues detected")
            self.identify_improvement_areas(cycle_results)
        else:
            print("✅ EXCELLENT - All tests passing!")
        
        self.improvement_cycles += 1
        return {
            'cycle': self.improvement_cycles,
            'avg_conversation_score': avg_conversation_score,
            'avg_context_score': avg_context_score,
            'needs_improvement': needs_improvement,
            'results': cycle_results
        }
    
    def identify_improvement_areas(self, cycle_results):
        """Identify specific areas that need improvement"""
        print("\n🔧 IMPROVEMENT AREAS IDENTIFIED:")
        
        all_issues = []
        for result in cycle_results:
            for conversation_result in result['results']:
                all_issues.extend(conversation_result['evaluation']['issues'])
        
        # Count issue frequency
        issue_counts = {}
        for issue in all_issues:
            issue_counts[issue] = issue_counts.get(issue, 0) + 1
        
        # Sort by frequency
        sorted_issues = sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)
        
        for issue, count in sorted_issues[:5]:  # Top 5 issues
            print(f"• {issue}: {count} occurrences")
        
        return sorted_issues
    
    def run_continuous_improvement_loop(self):
        """Run continuous testing until all tests pass or max cycles reached"""
        print("🚀 STARTING COMPREHENSIVE NON-TECHNICAL USER TESTING")
        print("🎯 Testing like real users with real business scenarios")
        print("🔄 Will run continuous improvement cycles until all tests pass")
        print("=" * 80)
        
        all_cycle_results = []
        
        while self.improvement_cycles < self.max_cycles:
            cycle_result = self.run_comprehensive_testing_cycle()
            all_cycle_results.append(cycle_result)
            
            if not cycle_result['needs_improvement']:
                print(f"\n🎉 SUCCESS! All tests passing after {self.improvement_cycles} cycles!")
                break
            
            if self.improvement_cycles < self.max_cycles:
                print(f"\n⏳ Waiting 30 seconds before next cycle...")
                time.sleep(30)
        
        # Generate final report
        self.generate_final_report(all_cycle_results)
        
        return all_cycle_results
    
    def generate_final_report(self, all_results):
        """Generate comprehensive final report"""
        print("\n📊 FINAL COMPREHENSIVE REPORT")
        print("=" * 80)
        
        if not all_results:
            print("❌ No test results available")
            return
        
        final_result = all_results[-1]
        
        print(f"Total Testing Cycles: {len(all_results)}")
        print(f"Final Conversation Quality: {final_result['avg_conversation_score']:.1f}%")
        print(f"Final Context Memory: {final_result['avg_context_score']:.1f}%")
        
        if final_result['avg_conversation_score'] >= 80 and final_result['avg_context_score'] >= 70:
            print("🏆 PRODUCTION READY - Chatbot passes all real user tests!")
        else:
            print("⚠️ NEEDS MORE WORK - Not ready for real users yet")
        
        # Save detailed results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"non_technical_user_test_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(all_results, f, indent=2, default=str)
        
        print(f"\n📄 Detailed results saved: {filename}")


def main():
    """Main execution function"""
    tester = NonTechnicalUserTester()
    
    # Check server connectivity
    try:
        response = requests.get(f"{tester.server_url}/health", timeout=5)
        if response.status_code != 200:
            print(f"❌ Server not responding at {tester.server_url}")
            return
    except:
        print(f"❌ Cannot connect to server at {tester.server_url}")
        return
    
    print("✅ Server connectivity confirmed")
    
    # Run comprehensive testing
    results = tester.run_continuous_improvement_loop()
    
    print("\n🎯 TESTING COMPLETE!")
    print("Check the generated report for detailed analysis.")


if __name__ == "__main__":
    main()
