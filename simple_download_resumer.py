#!/usr/bin/env python3
"""
Simple LLM Download Resumer - No Unicode, Just Downloads
"""

import os
import sys
import time

def setup_environment():
    """Setup download environment"""
    print("Setting up download environment...")
    
    # Clear offline modes
    if 'TRANSFORMERS_OFFLINE' in os.environ:
        del os.environ['TRANSFORMERS_OFFLINE']
    if 'HF_HUB_OFFLINE' in os.environ:
        del os.environ['HF_HUB_OFFLINE']
    
    # Set long timeouts
    os.environ['HF_HUB_DOWNLOAD_TIMEOUT'] = '3600'  # 1 hour
    os.environ['CURL_CA_BUNDLE'] = ''
    os.environ['REQUESTS_CA_BUNDLE'] = ''
    
    print("Environment configured")

def download_model(model_name):
    """Download a single model with resume"""
    print(f"\nDownloading {model_name}...")
    
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        import torch
        
        print("Step 1: Downloading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(
            model_name,
            resume_download=True,
            force_download=False
        )
        print("Tokenizer completed")
        
        print("Step 2: Downloading model...")
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            resume_download=True,
            force_download=False,
            torch_dtype=torch.float32,
            low_cpu_mem_usage=True
        )
        print("Model completed")
        
        print("Step 3: Testing model...")
        from transformers import pipeline
        pipe = pipeline('text-generation', model=model, tokenizer=tokenizer)
        result = pipe("Hello", max_length=20)
        print(f"Test successful: {result[0]['generated_text'][:50]}")
        
        return True
        
    except Exception as e:
        print(f"Download failed: {e}")
        return False

def check_cache():
    """Check what's in cache"""
    print("Checking cache...")
    
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        
        models = [
            'microsoft/DialoGPT-medium',
            'microsoft/DialoGPT-small', 
            'distilgpt2',
            'gpt2'
        ]
        
        for model in models:
            try:
                tokenizer = AutoTokenizer.from_pretrained(model, local_files_only=True)
                model_obj = AutoModelForCausalLM.from_pretrained(model, local_files_only=True)
                print(f"CACHED: {model}")
            except:
                print(f"NOT CACHED: {model}")
                
    except Exception as e:
        print(f"Cache check failed: {e}")

def main():
    """Main function"""
    print("SIMPLE LLM DOWNLOAD RESUMER")
    print("=" * 50)
    
    setup_environment()
    check_cache()
    
    # Try to download models in order
    models = [
        'microsoft/DialoGPT-small',    # Start with smaller one
        'distilgpt2',                  # Even smaller
        'gpt2',                        # Fallback
        'microsoft/DialoGPT-medium'    # Try medium last
    ]
    
    for model in models:
        print(f"\n{'='*50}")
        success = download_model(model)
        if success:
            print(f"SUCCESS: {model} is ready!")
            break
        else:
            print(f"FAILED: {model}")
            time.sleep(5)  # Wait before next attempt
    
    print("\nDownload process completed")

if __name__ == "__main__":
    main()
