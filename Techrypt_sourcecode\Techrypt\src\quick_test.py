#!/usr/bin/env python3
"""
Quick Test for Techrypt AI Chatbot
Tests basic functionality and name handling
"""

import requests
import json
import time

def test_server_connection():
    """Test if server is running"""
    try:
        response = requests.get("http://127.0.0.1:5000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running!")
            return True
        else:
            print(f"❌ Server responded with status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Server connection failed: {e}")
        return False

def test_chat_response(message, name="", expected_not_contain=None):
    """Test a single chat message"""
    try:
        payload = {
            "message": message,
            "name": name
        }
        
        response = requests.post(
            "http://127.0.0.1:5000/chat",
            json=payload,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            ai_response = result.get("response", "")
            
            print(f"Input: '{message}' (name: '{name}')")
            print(f"Response: {ai_response[:100]}...")
            
            # Check for forbidden content
            issues = []
            if expected_not_contain:
                for forbidden in expected_not_contain:
                    if forbidden.lower() in ai_response.lower():
                        issues.append(f"Contains forbidden word: {forbidden}")
            
            if issues:
                print(f"❌ ISSUES: {', '.join(issues)}")
                return False
            else:
                print("✅ PASS")
                return True
        else:
            print(f"❌ HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def main():
    """Run quick tests"""
    print("🚀 Quick Test for Techrypt AI Chatbot")
    print("=" * 50)
    
    # Test server connection
    if not test_server_connection():
        return
    
    print("\n📋 Running Quick Tests...")
    
    # Test cases
    test_cases = [
        {
            "message": "hello",
            "name": "",
            "expected_not_contain": ["zain", "Zain"]
        },
        {
            "message": "I have an ecommerce business",
            "name": "",
            "expected_not_contain": ["zain", "Zain"]
        },
        {
            "message": "I have a restaurant",
            "name": "John",
            "expected_not_contain": ["zain", "Zain"]
        },
        {
            "message": "what services do you offer",
            "name": "",
            "expected_not_contain": ["zain", "Zain"]
        },
        {
            "message": "I need website development",
            "name": "",
            "expected_not_contain": ["zain", "Zain"]
        }
    ]
    
    passed = 0
    total = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/{total}:")
        if test_chat_response(
            test_case["message"], 
            test_case.get("name", ""),
            test_case.get("expected_not_contain", [])
        ):
            passed += 1
        time.sleep(0.5)  # Small delay between tests
    
    # Results
    print("\n" + "=" * 50)
    print("📊 QUICK TEST RESULTS")
    print("=" * 50)
    print(f"✅ Passed: {passed}/{total} ({passed/total*100:.1f}%)")
    print(f"❌ Failed: {total-passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Chatbot is working correctly!")
    elif passed >= total * 0.8:
        print("✅ GOOD! Most tests passed!")
    else:
        print("⚠️  ISSUES FOUND! Some tests failed!")

if __name__ == "__main__":
    main()
