#!/usr/bin/env python3
"""
🗄️ MONGODB BACKEND FOR INTELLIGENT CHATBOT
Production-ready database with appointment scheduling and validations
"""

import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import pymongo
from pymongo import MongoClient
from bson import ObjectId
import hashlib
import re

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TechryptMongoDB:
    def __init__(self, connection_string: str = None):
        """Initialize MongoDB connection with production settings"""
        try:
            # Use environment variable or default local connection
            self.connection_string = connection_string or os.getenv(
                'MONGODB_URI', 
                'mongodb://localhost:27017/'
            )
            
            # Connect to MongoDB with production settings
            self.client = MongoClient(
                self.connection_string,
                serverSelectionTimeoutMS=5000,  # 5 second timeout
                connectTimeoutMS=10000,         # 10 second connection timeout
                maxPoolSize=50,                 # Support up to 50 concurrent connections
                retryWrites=True               # Enable retry writes for reliability
            )
            
            # Database and collections
            self.db = self.client.techrypt_chatbot
            self.users = self.db.users
            self.conversations = self.db.conversations
            self.appointments = self.db.appointments
            self.analytics = self.db.analytics
            self.business_intelligence = self.db.business_intelligence
            
            # Create indexes for performance
            self._create_indexes()
            
            logger.info("✅ MongoDB connected successfully")
            
        except Exception as e:
            logger.error(f"❌ MongoDB connection failed: {e}")
            raise
    
    def _create_indexes(self):
        """Create database indexes for optimal performance"""
        try:
            # User indexes
            self.users.create_index("email", unique=True)
            self.users.create_index("phone")
            self.users.create_index("created_at")
            
            # Conversation indexes
            self.conversations.create_index([("user_id", 1), ("timestamp", -1)])
            self.conversations.create_index("session_id")
            
            # Appointment indexes
            self.appointments.create_index([("date", 1), ("time", 1)])
            self.appointments.create_index("user_id")
            self.appointments.create_index("status")
            self.appointments.create_index([("date", 1), ("status", 1)])
            
            # Analytics indexes
            self.analytics.create_index([("date", 1), ("metric", 1)])
            
            logger.info("✅ Database indexes created")
            
        except Exception as e:
            logger.warning(f"⚠️ Index creation warning: {e}")
    
    def create_user(self, name: str, email: str = None, phone: str = None, 
                   business_type: str = None) -> str:
        """Create or update user with validation"""
        try:
            # Validate inputs
            if not name or len(name.strip()) < 2:
                raise ValueError("Name must be at least 2 characters")
            
            if email and not self._validate_email(email):
                raise ValueError("Invalid email format")
            
            if phone and not self._validate_phone(phone):
                raise ValueError("Invalid phone format")
            
            # Check if user exists
            existing_user = None
            if email:
                existing_user = self.users.find_one({"email": email})
            
            if existing_user:
                # Update existing user
                update_data = {
                    "name": name.strip(),
                    "last_updated": datetime.utcnow()
                }
                if phone:
                    update_data["phone"] = phone
                if business_type:
                    update_data["business_type"] = business_type
                
                self.users.update_one(
                    {"_id": existing_user["_id"]},
                    {"$set": update_data}
                )
                return str(existing_user["_id"])
            else:
                # Create new user
                user_data = {
                    "name": name.strip(),
                    "email": email,
                    "phone": phone,
                    "business_type": business_type,
                    "created_at": datetime.utcnow(),
                    "last_updated": datetime.utcnow(),
                    "conversation_count": 0,
                    "appointment_count": 0
                }
                
                result = self.users.insert_one(user_data)
                logger.info(f"✅ User created: {name}")
                return str(result.inserted_id)
                
        except Exception as e:
            logger.error(f"❌ User creation failed: {e}")
            raise
    
    def save_conversation(self, user_id: str, user_message: str, 
                         bot_response: str, session_id: str = None) -> str:
        """Save conversation with context"""
        try:
            conversation_data = {
                "user_id": user_id,
                "session_id": session_id or self._generate_session_id(),
                "user_message": user_message,
                "bot_response": bot_response,
                "timestamp": datetime.utcnow(),
                "message_length": len(user_message),
                "response_length": len(bot_response)
            }
            
            result = self.conversations.insert_one(conversation_data)
            
            # Update user conversation count
            self.users.update_one(
                {"_id": ObjectId(user_id)},
                {"$inc": {"conversation_count": 1}}
            )
            
            return str(result.inserted_id)
            
        except Exception as e:
            logger.error(f"❌ Conversation save failed: {e}")
            raise
    
    def schedule_appointment(self, user_id: str, date: str, time: str, 
                           service: str, notes: str = None) -> Dict[str, Any]:
        """Schedule appointment with comprehensive validation"""
        try:
            # Parse and validate date/time
            appointment_datetime = self._parse_datetime(date, time)
            
            # Validate appointment is in future
            if appointment_datetime <= datetime.now():
                return {
                    "success": False,
                    "error": "Appointment must be scheduled for future date/time"
                }
            
            # Check if slot is available
            if not self._is_slot_available(appointment_datetime):
                # Find next available slot
                next_slot = self._find_next_available_slot(appointment_datetime)
                return {
                    "success": False,
                    "error": "Time slot not available",
                    "suggested_time": next_slot.strftime("%Y-%m-%d %H:%M")
                }
            
            # Check user's existing appointments
            existing_today = self.appointments.find_one({
                "user_id": user_id,
                "date": appointment_datetime.date().isoformat(),
                "status": {"$in": ["scheduled", "confirmed"]}
            })
            
            if existing_today:
                return {
                    "success": False,
                    "error": "You already have an appointment scheduled for this date"
                }
            
            # Create appointment
            appointment_data = {
                "user_id": user_id,
                "date": appointment_datetime.date().isoformat(),
                "time": appointment_datetime.time().isoformat(),
                "datetime": appointment_datetime,
                "service": service,
                "notes": notes,
                "status": "scheduled",
                "created_at": datetime.utcnow(),
                "reminder_sent": False
            }
            
            result = self.appointments.insert_one(appointment_data)
            
            # Update user appointment count
            self.users.update_one(
                {"_id": ObjectId(user_id)},
                {"$inc": {"appointment_count": 1}}
            )
            
            logger.info(f"✅ Appointment scheduled: {appointment_datetime}")
            
            return {
                "success": True,
                "appointment_id": str(result.inserted_id),
                "datetime": appointment_datetime.strftime("%Y-%m-%d %H:%M"),
                "service": service
            }
            
        except Exception as e:
            logger.error(f"❌ Appointment scheduling failed: {e}")
            return {
                "success": False,
                "error": f"Scheduling failed: {str(e)}"
            }
    
    def get_user_context(self, user_id: str) -> Dict[str, Any]:
        """Get comprehensive user context"""
        try:
            user = self.users.find_one({"_id": ObjectId(user_id)})
            if not user:
                return {}
            
            # Get recent conversations
            recent_conversations = list(self.conversations.find(
                {"user_id": user_id}
            ).sort("timestamp", -1).limit(5))
            
            # Get upcoming appointments
            upcoming_appointments = list(self.appointments.find({
                "user_id": user_id,
                "datetime": {"$gte": datetime.now()},
                "status": {"$in": ["scheduled", "confirmed"]}
            }).sort("datetime", 1))
            
            return {
                "user": user,
                "recent_conversations": recent_conversations,
                "upcoming_appointments": upcoming_appointments,
                "total_conversations": user.get("conversation_count", 0),
                "total_appointments": user.get("appointment_count", 0)
            }
            
        except Exception as e:
            logger.error(f"❌ User context retrieval failed: {e}")
            return {}
    
    def _validate_email(self, email: str) -> bool:
        """Validate email format"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    def _validate_phone(self, phone: str) -> bool:
        """Validate phone format"""
        # Remove all non-digits
        digits_only = re.sub(r'\D', '', phone)
        # Check if it's 10-15 digits
        return 10 <= len(digits_only) <= 15
    
    def _parse_datetime(self, date: str, time: str) -> datetime:
        """Parse date and time strings"""
        try:
            # Handle various date formats
            date_formats = ["%Y-%m-%d", "%m/%d/%Y", "%d/%m/%Y"]
            time_formats = ["%H:%M", "%I:%M %p", "%H:%M:%S"]
            
            parsed_date = None
            for fmt in date_formats:
                try:
                    parsed_date = datetime.strptime(date, fmt).date()
                    break
                except ValueError:
                    continue
            
            if not parsed_date:
                raise ValueError(f"Invalid date format: {date}")
            
            parsed_time = None
            for fmt in time_formats:
                try:
                    parsed_time = datetime.strptime(time, fmt).time()
                    break
                except ValueError:
                    continue
            
            if not parsed_time:
                raise ValueError(f"Invalid time format: {time}")
            
            return datetime.combine(parsed_date, parsed_time)
            
        except Exception as e:
            raise ValueError(f"Date/time parsing failed: {e}")
    
    def _is_slot_available(self, appointment_datetime: datetime) -> bool:
        """Check if appointment slot is available"""
        # Check for conflicts (30-minute buffer)
        start_buffer = appointment_datetime - timedelta(minutes=15)
        end_buffer = appointment_datetime + timedelta(minutes=15)
        
        conflict = self.appointments.find_one({
            "datetime": {
                "$gte": start_buffer,
                "$lte": end_buffer
            },
            "status": {"$in": ["scheduled", "confirmed"]}
        })
        
        return conflict is None
    
    def _find_next_available_slot(self, preferred_datetime: datetime) -> datetime:
        """Find next available appointment slot"""
        current = preferred_datetime
        
        # Check next 7 days
        for _ in range(7 * 24 * 2):  # 30-minute intervals
            if self._is_slot_available(current):
                # Check business hours (9 AM - 6 PM)
                if 9 <= current.hour < 18:
                    return current
            current += timedelta(minutes=30)
        
        # If no slot found in 7 days, return 7 days later at 9 AM
        return preferred_datetime + timedelta(days=7)
    
    def _generate_session_id(self) -> str:
        """Generate unique session ID"""
        return hashlib.md5(
            f"{datetime.utcnow().isoformat()}".encode()
        ).hexdigest()[:16]
    
    def close_connection(self):
        """Close MongoDB connection"""
        if self.client:
            self.client.close()
            logger.info("✅ MongoDB connection closed")

# Global MongoDB instance
mongodb_instance = None

def get_mongodb() -> TechryptMongoDB:
    """Get MongoDB instance (singleton pattern)"""
    global mongodb_instance
    if mongodb_instance is None:
        mongodb_instance = TechryptMongoDB()
    return mongodb_instance
