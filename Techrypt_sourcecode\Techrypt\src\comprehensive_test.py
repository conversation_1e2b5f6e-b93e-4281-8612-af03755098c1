#!/usr/bin/env python3
"""
Comprehensive Testing System for Techrypt AI Chatbot
Tests with 10,000+ diverse inputs to validate responses
"""

import requests
import json
import time
import random
from typing import List, Dict, Any
import csv
from datetime import datetime

class TechryptTester:
    def __init__(self, base_url: str = "http://127.0.0.1:5000"):
        self.base_url = base_url
        self.test_results = []
        self.failed_tests = []
        self.passed_tests = []

    def generate_test_inputs(self) -> List[Dict[str, Any]]:
        """Generate 10,000+ diverse test inputs"""
        test_inputs = []

        # 1. Business Type Tests (2000 inputs)
        business_types = [
            "ecommerce store", "restaurant", "bakery", "salon", "gym", "clinic",
            "law firm", "accounting firm", "consulting firm", "real estate",
            "coffee shop", "bookstore", "pharmacy", "dental clinic", "spa",
            "fitness center", "yoga studio", "dance studio", "music school",
            "tutoring center", "daycare", "pet grooming", "veterinary clinic",
            "auto repair", "plumbing service", "electrical service", "cleaning service",
            "landscaping", "photography studio", "wedding planning", "catering",
            "food truck", "juice bar", "smoothie shop", "ice cream shop",
            "clothing store", "jewelry store", "electronics store", "furniture store",
            "home decor", "art gallery", "craft store", "toy store", "sports store",
            "bike shop", "car dealership", "insurance agency", "travel agency",
            "marketing agency", "web design agency", "advertising agency",
            "construction company", "architecture firm", "engineering firm",
            "software company", "tech startup", "mobile app development",
            "game development", "digital marketing", "social media agency",
            "content creation", "video production", "graphic design",
            "printing service", "packaging company", "logistics company",
            "shipping service", "courier service", "taxi service", "rideshare",
            "hotel", "bed and breakfast", "vacation rental", "event venue",
            "conference center", "coworking space", "storage facility",
            "security service", "IT support", "computer repair", "phone repair",
            "appliance repair", "HVAC service", "roofing company", "painting service",
            "flooring company", "tile installation", "carpet cleaning",
            "window cleaning", "pest control", "lawn care", "tree service",
            "pool service", "handyman service", "moving company", "junk removal",
            "recycling service", "waste management", "environmental consulting",
            "renewable energy", "solar installation", "wind energy",
            "water treatment", "air quality testing", "mold remediation",
            "fire damage restoration", "flood damage restoration", "disaster recovery"
        ]

        business_patterns = [
            "I have a {business}",
            "I own a {business}",
            "I run a {business}",
            "My {business}",
            "Help me with my {business}",
            "I need help with my {business}",
            "How can you help my {business}",
            "What can you do for my {business}",
            "I'm starting a {business}",
            "I want to grow my {business}"
        ]

        for business in business_types:
            for pattern in business_patterns:
                test_inputs.append({
                    "input": pattern.format(business=business),
                    "category": "business_type",
                    "expected_keywords": ["Great", business, "services", "consultation"],
                    "should_not_contain": ["zain", "Zain"]
                })

        # 2. Service Requests (1500 inputs)
        services = [
            "website development", "web development", "website", "web design",
            "social media marketing", "smm", "instagram", "facebook", "linkedin",
            "branding services", "branding", "logo design", "brand identity",
            "chatbot development", "chatbot", "ai chatbot", "automation",
            "automation packages", "business automation", "process automation",
            "payment gateway", "payment integration", "stripe", "paypal"
        ]

        service_patterns = [
            "I need {service}",
            "I want {service}",
            "Can you help with {service}",
            "Tell me about {service}",
            "How much for {service}",
            "I'm interested in {service}",
            "{service} please",
            "Do you offer {service}",
            "What is {service}",
            "Explain {service}"
        ]

        for service in services:
            for pattern in service_patterns:
                test_inputs.append({
                    "input": pattern.format(service=service),
                    "category": "service_request",
                    "expected_keywords": [service, "consultation", "schedule"],
                    "should_not_contain": ["zain", "Zain"]
                })

        # 3. Greetings and General Queries (1000 inputs)
        greetings = [
            "hello", "hi", "hey", "good morning", "good afternoon", "good evening",
            "greetings", "howdy", "what's up", "how are you", "nice to meet you"
        ]

        general_queries = [
            "what do you do", "what services do you offer", "tell me about your company",
            "how can you help me", "what is techrypt", "about techrypt",
            "your services", "list services", "show me services", "help me",
            "what can you do for me", "how does this work", "tell me more"
        ]

        for greeting in greetings:
            test_inputs.append({
                "input": greeting,
                "category": "greeting",
                "expected_keywords": ["Hello", "Welcome", "Techrypt", "assist"],
                "should_not_contain": ["zain", "Zain"]
            })

        for query in general_queries:
            test_inputs.append({
                "input": query,
                "category": "general_query",
                "expected_keywords": ["services", "Techrypt", "help"],
                "should_not_contain": ["zain", "Zain"]
            })

        # 4. Appointment and Booking (800 inputs)
        appointment_requests = [
            "book appointment", "schedule appointment", "I want to book",
            "can I schedule", "appointment please", "book a meeting",
            "schedule consultation", "I need appointment", "when can we meet",
            "available times", "book a call", "schedule a call"
        ]

        for request in appointment_requests:
            for i in range(20):  # Multiple variations
                test_inputs.append({
                    "input": request,
                    "category": "appointment",
                    "expected_keywords": ["appointment", "schedule", "consultation"],
                    "should_not_contain": ["zain", "Zain"]
                })

        # 5. Pricing and Cost Queries (700 inputs)
        pricing_queries = [
            "how much", "what's the cost", "pricing", "price", "cost",
            "how much does it cost", "what are your rates", "budget",
            "expensive", "cheap", "affordable", "quote", "estimate"
        ]

        for query in pricing_queries:
            for i in range(20):
                test_inputs.append({
                    "input": query,
                    "category": "pricing",
                    "expected_keywords": ["pricing", "consultation", "quote"],
                    "should_not_contain": ["zain", "Zain"]
                })

        # 6. Random and Edge Cases (1000 inputs)
        edge_cases = [
            "", "   ", "test", "random", "asdfgh", "123456", "!@#$%^",
            "very long message " * 50, "CAPS LOCK MESSAGE",
            "mixed CaSe MeSsAgE", "numbers 123 and text",
            "special chars !@#$%^&*()", "unicode 🚀🎉✨",
            "multiple    spaces", "trailing spaces   ",
            "   leading spaces", "new\nlines\nhere"
        ]

        for case in edge_cases:
            for i in range(10):
                test_inputs.append({
                    "input": case,
                    "category": "edge_case",
                    "expected_keywords": ["Techrypt", "services"],
                    "should_not_contain": ["zain", "Zain", "error", "Error"]
                })

        # 7. Name Testing (2000 inputs)
        test_names = [
            "", "John", "Sarah", "Ahmed", "Maria", "David", "Lisa", "Mohammed",
            "Anna", "Carlos", "Jennifer", "Ali", "Emma", "Hassan", "Sophie"
        ]

        for name in test_names:
            for business in business_types[:20]:  # Test with subset
                test_inputs.append({
                    "input": f"I have a {business}",
                    "category": "name_test",
                    "name": name,
                    "expected_keywords": ["Great", business],
                    "should_not_contain": ["zain", "Zain"] if name != "zain" else []
                })

        # 8. Stress Testing (1000 inputs)
        for i in range(1000):
            random_business = random.choice(business_types)
            random_pattern = random.choice(business_patterns)
            test_inputs.append({
                "input": random_pattern.format(business=random_business),
                "category": "stress_test",
                "expected_keywords": ["Great", "services"],
                "should_not_contain": ["zain", "Zain"]
            })

        print(f"Generated {len(test_inputs)} test inputs")
        return test_inputs

    def test_single_input(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """Test a single input and return results"""
        try:
            # Prepare request
            payload = {
                "message": test_case["input"],
                "name": test_case.get("name", "")
            }

            # Send request
            response = requests.post(
                f"{self.base_url}/chat",
                json=payload,
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                ai_response = result.get("response", "")

                # Validate response
                validation_result = self.validate_response(ai_response, test_case)

                return {
                    "input": test_case["input"],
                    "category": test_case["category"],
                    "name": test_case.get("name", ""),
                    "response": ai_response,
                    "status": "PASS" if validation_result["valid"] else "FAIL",
                    "validation": validation_result,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {
                    "input": test_case["input"],
                    "category": test_case["category"],
                    "status": "FAIL",
                    "error": f"HTTP {response.status_code}",
                    "timestamp": datetime.now().isoformat()
                }

        except Exception as e:
            return {
                "input": test_case["input"],
                "category": test_case["category"],
                "status": "ERROR",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def validate_response(self, response: str, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """Validate AI response against test case expectations"""
        validation = {
            "valid": True,
            "issues": []
        }

        # Check for forbidden content
        for forbidden in test_case.get("should_not_contain", []):
            if forbidden.lower() in response.lower():
                validation["valid"] = False
                validation["issues"].append(f"Contains forbidden word: {forbidden}")

        # Check for expected keywords (at least one should be present)
        expected_keywords = test_case.get("expected_keywords", [])
        if expected_keywords:
            found_keywords = [kw for kw in expected_keywords if kw.lower() in response.lower()]
            if not found_keywords:
                validation["valid"] = False
                validation["issues"].append(f"Missing expected keywords: {expected_keywords}")

        # Check response length
        if len(response.strip()) < 10:
            validation["valid"] = False
            validation["issues"].append("Response too short")

        # Check for empty response
        if not response.strip():
            validation["valid"] = False
            validation["issues"].append("Empty response")

        return validation

    def run_comprehensive_test(self, max_tests: int = 10000):
        """Run comprehensive testing with up to max_tests inputs"""
        print(f"🚀 Starting Comprehensive Techrypt AI Testing...")
        print(f"📊 Target: {max_tests} test cases")
        print("=" * 60)

        # Generate test inputs
        test_inputs = self.generate_test_inputs()

        # Limit to max_tests
        if len(test_inputs) > max_tests:
            test_inputs = test_inputs[:max_tests]

        print(f"✅ Generated {len(test_inputs)} test cases")
        print("🔄 Starting execution...")

        # Run tests
        start_time = time.time()
        for i, test_case in enumerate(test_inputs):
            if i % 100 == 0:
                print(f"Progress: {i}/{len(test_inputs)} ({i/len(test_inputs)*100:.1f}%)")

            result = self.test_single_input(test_case)
            self.test_results.append(result)

            if result["status"] == "PASS":
                self.passed_tests.append(result)
            else:
                self.failed_tests.append(result)

            # Small delay to avoid overwhelming the server
            time.sleep(0.01)

        end_time = time.time()

        # Generate report
        self.generate_report(end_time - start_time)

    def generate_report(self, execution_time: float):
        """Generate comprehensive test report"""
        total_tests = len(self.test_results)
        passed_tests = len(self.passed_tests)
        failed_tests = len(self.failed_tests)
        pass_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0

        print("\n" + "=" * 60)
        print("📋 COMPREHENSIVE TEST REPORT")
        print("=" * 60)
        print(f"⏱️  Execution Time: {execution_time:.2f} seconds")
        print(f"📊 Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests} ({pass_rate:.1f}%)")
        print(f"❌ Failed: {failed_tests} ({100-pass_rate:.1f}%)")
        print(f"🎯 Success Rate: {pass_rate:.1f}%")

        # Category breakdown
        print("\n📈 RESULTS BY CATEGORY:")
        categories = {}
        for result in self.test_results:
            cat = result.get("category", "unknown")
            if cat not in categories:
                categories[cat] = {"total": 0, "passed": 0}
            categories[cat]["total"] += 1
            if result["status"] == "PASS":
                categories[cat]["passed"] += 1

        for category, stats in categories.items():
            cat_pass_rate = (stats["passed"] / stats["total"] * 100) if stats["total"] > 0 else 0
            print(f"  {category}: {stats['passed']}/{stats['total']} ({cat_pass_rate:.1f}%)")

        # Critical issues
        print("\n🚨 CRITICAL ISSUES:")
        zain_issues = []
        error_issues = []

        for result in self.failed_tests:
            validation = result.get("validation", {})
            issues = validation.get("issues", [])

            for issue in issues:
                if "zain" in issue.lower():
                    zain_issues.append(result)
                elif "error" in issue.lower():
                    error_issues.append(result)

        if zain_issues:
            print(f"  ⚠️  Name Issues (zain): {len(zain_issues)} cases")
        if error_issues:
            print(f"  ⚠️  Error Responses: {len(error_issues)} cases")

        # Save detailed results
        self.save_results()

        print(f"\n💾 Detailed results saved to test_results.csv")
        print("=" * 60)

        # Final verdict
        if pass_rate >= 95:
            print("🎉 EXCELLENT! Chatbot performance is outstanding!")
        elif pass_rate >= 90:
            print("✅ GOOD! Chatbot performance is solid!")
        elif pass_rate >= 80:
            print("⚠️  ACCEPTABLE! Some improvements needed!")
        else:
            print("❌ NEEDS WORK! Significant issues found!")

    def save_results(self):
        """Save test results to CSV file"""
        with open('test_results.csv', 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['input', 'category', 'name', 'response', 'status', 'issues', 'timestamp']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            writer.writeheader()
            for result in self.test_results:
                validation = result.get("validation", {})
                issues = "; ".join(validation.get("issues", []))

                writer.writerow({
                    'input': result.get('input', ''),
                    'category': result.get('category', ''),
                    'name': result.get('name', ''),
                    'response': result.get('response', ''),
                    'status': result.get('status', ''),
                    'issues': issues,
                    'timestamp': result.get('timestamp', '')
                })

def main():
    """Main execution function"""
    print("🤖 Techrypt AI Comprehensive Testing System")
    print("=" * 50)

    # Check if server is running
    tester = TechryptTester()
    try:
        response = requests.get(f"{tester.base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ AI Server is running!")
        else:
            print("❌ AI Server not responding properly!")
            return
    except:
        print("❌ AI Server is not running! Please start it first.")
        return

    # Run comprehensive test
    tester.run_comprehensive_test(max_tests=10000)

if __name__ == "__main__":
    main()
