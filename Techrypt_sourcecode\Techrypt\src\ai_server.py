#!/usr/bin/env python3
"""
Flask API Server for Techrypt AI Chatbot
Serves intelligent responses via REST API
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
from datetime import datetime
import logging
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ai_backend import get_ai_response

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for React frontend

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'Techrypt AI Chatbot',
        'version': '1.0.0'
    })

@app.route('/chat', methods=['POST'])
def chat():
    """Main chat endpoint"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': 'No data provided'}), 400

        user_message = data.get('message', '').strip()
        user_name = data.get('user_name', '').strip()

        # Handle empty messages gracefully (don't return error)
        if not user_message:
            user_message = "hello"  # Treat empty message as greeting

        logger.info(f"Received message: {user_message} from user: {user_name}")

        # Get AI response
        ai_response = get_ai_response(user_message, user_name)

        logger.info(f"Generated response: {ai_response[:100]}...")

        return jsonify({
            'response': ai_response,
            'status': 'success',
            'timestamp': str(datetime.now())
        })

    except Exception as e:
        logger.error(f"Error in chat endpoint: {e}")
        return jsonify({
            'error': 'Internal server error',
            'message': 'Sorry, I encountered an error. Please try again.'
        }), 500

@app.route('/context', methods=['GET'])
def get_context():
    """Get current conversation context"""
    try:
        from ai_backend import techrypt_ai

        return jsonify({
            'user_context': techrypt_ai.user_context,
            'conversation_length': len(techrypt_ai.conversation_history),
            'status': 'success'
        })

    except Exception as e:
        logger.error(f"Error getting context: {e}")
        return jsonify({'error': 'Failed to get context'}), 500

@app.route('/reset', methods=['POST'])
def reset_conversation():
    """Reset conversation history"""
    try:
        from ai_backend import techrypt_ai

        techrypt_ai.conversation_history = []
        techrypt_ai.user_context = {
            'name': '',
            'business_type': '',
            'interests': [],
            'previous_topics': [],
            'services_discussed': []
        }

        return jsonify({
            'message': 'Conversation reset successfully',
            'status': 'success'
        })

    except Exception as e:
        logger.error(f"Error resetting conversation: {e}")
        return jsonify({'error': 'Failed to reset conversation'}), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':

    print("🤖 Starting Techrypt AI Server...")
    print("📡 Server will be available at: http://localhost:5000")
    print("🔗 Health check: http://localhost:5000/health")
    print("💬 Chat endpoint: POST http://localhost:5000/chat")
    print("📊 Context endpoint: GET http://localhost:5000/context")
    print("🔄 Reset endpoint: POST http://localhost:5000/reset")

    # Run the server
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=False,  # Disable debug mode to prevent restarts
        threaded=True
    )
