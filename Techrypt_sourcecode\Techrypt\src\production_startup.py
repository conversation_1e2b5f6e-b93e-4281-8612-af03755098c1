#!/usr/bin/env python3
"""
🚀 PRODUCTION STARTUP SCRIPT
Handles all dependencies, downloads, and system initialization
"""

import subprocess
import sys
import os
import time
from datetime import datetime

def install_dependencies():
    """Install required dependencies for production"""
    print("📦 Installing production dependencies...")
    
    dependencies = [
        'pymongo',
        'flask',
        'flask-cors',
        'transformers',
        'torch',
        'scikit-learn',
        'pandas',
        'numpy',
        'requests'
    ]
    
    for dep in dependencies:
        try:
            print(f"Installing {dep}...")
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', dep
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print(f"✅ {dep} installed successfully")
            else:
                print(f"⚠️ {dep} installation warning: {result.stderr}")
        except Exception as e:
            print(f"❌ {dep} installation failed: {e}")

def download_llm_models():
    """Download DialoGPT-medium model"""
    print("\n🤖 Downloading DialoGPT-medium model...")
    
    try:
        download_script = '''
import os
os.environ['HF_HUB_DISABLE_PROGRESS_BARS'] = '0'
from transformers import AutoTokenizer, AutoModelForCausalLM
import torch

print("📥 Downloading DialoGPT-medium tokenizer...")
tokenizer = AutoTokenizer.from_pretrained('microsoft/DialoGPT-medium')
print("✅ Tokenizer downloaded")

print("📥 Downloading DialoGPT-medium model...")
model = AutoModelForCausalLM.from_pretrained(
    'microsoft/DialoGPT-medium',
    torch_dtype=torch.float32,
    low_cpu_mem_usage=True
)
print("✅ DialoGPT-medium downloaded and cached!")

# Test the model
print("🧪 Testing model...")
from transformers import pipeline
chatbot = pipeline('text-generation', model=model, tokenizer=tokenizer)
test_result = chatbot("Hello", max_length=20)
print(f"✅ Model test successful: {test_result[0]['generated_text']}")
'''
        
        result = subprocess.run([
            sys.executable, '-c', download_script
        ], capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✅ DialoGPT-medium downloaded successfully!")
            print(result.stdout)
        else:
            print("⚠️ Model download had issues:")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ Model download failed: {e}")

def setup_mongodb():
    """Setup MongoDB (local or cloud)"""
    print("\n🗄️ Setting up MongoDB...")
    
    # Check if MongoDB is available locally
    try:
        import pymongo
        client = pymongo.MongoClient('mongodb://localhost:27017/', serverSelectionTimeoutMS=2000)
        client.server_info()
        print("✅ Local MongoDB detected and working")
        return True
    except:
        print("⚠️ Local MongoDB not available")
        print("💡 System will work without MongoDB (using in-memory storage)")
        return False

def start_production_server():
    """Start the production server"""
    print("\n🚀 Starting production server...")
    
    try:
        # Start AI server
        subprocess.Popen([
            sys.executable, 'ai_server.py'
        ], cwd=os.path.dirname(__file__))
        
        print("✅ AI Server started on http://localhost:5000")
        
        # Start React frontend (if available)
        frontend_path = os.path.join(os.path.dirname(__file__), '..')
        if os.path.exists(os.path.join(frontend_path, 'package.json')):
            try:
                subprocess.Popen(['npm', 'start'], cwd=frontend_path)
                print("✅ React frontend starting on http://localhost:3000")
            except:
                print("⚠️ React frontend not started (npm not available)")
        
        return True
        
    except Exception as e:
        print(f"❌ Server startup failed: {e}")
        return False

def run_comprehensive_tests():
    """Run comprehensive system tests"""
    print("\n🧪 Running comprehensive tests...")
    
    test_script = '''
import requests
import time

# Wait for server to start
time.sleep(5)

print("Testing server connectivity...")
try:
    response = requests.get('http://localhost:5000/health', timeout=10)
    if response.status_code == 200:
        print("✅ Server is responding")
    else:
        print(f"❌ Server error: {response.status_code}")
        exit(1)
except Exception as e:
    print(f"❌ Server not reachable: {e}")
    exit(1)

# Test chatbot functionality
test_messages = [
    "hello",
    "i run a cleaning company how can you help me",
    "what business do i have?",
    "i need a website and social media marketing",
    "how much does everything cost",
    "can you schedule a consultation"
]

print("\\nTesting chatbot responses...")
for i, message in enumerate(test_messages, 1):
    try:
        response = requests.post(
            'http://localhost:5000/chat',
            json={'message': message, 'user_name': 'testuser'},
            timeout=15
        )
        
        if response.status_code == 200:
            result = response.json()
            ai_response = result.get('response', '')
            
            # Check response quality
            quality_score = 0
            if 'techrypt' in ai_response.lower():
                quality_score += 25
            if len(ai_response) > 50:
                quality_score += 25
            if any(word in ai_response.lower() for word in ['help', 'can', 'will']):
                quality_score += 25
            if any(word in ai_response.lower() for word in ['consultation', 'schedule']):
                quality_score += 25
            
            print(f"Test {i}: {quality_score}% - {message[:30]}...")
            if quality_score < 50:
                print(f"  ⚠️ Low quality response: {ai_response[:100]}...")
        else:
            print(f"Test {i}: ERROR - {response.status_code}")
            
    except Exception as e:
        print(f"Test {i}: ERROR - {e}")
    
    time.sleep(1)

print("\\n✅ Comprehensive testing completed!")
'''
    
    try:
        result = subprocess.run([
            sys.executable, '-c', test_script
        ], capture_output=True, text=True, timeout=180)
        
        print(result.stdout)
        if result.stderr:
            print("Test warnings:", result.stderr)
            
    except Exception as e:
        print(f"❌ Testing failed: {e}")

def main():
    """Main production startup sequence"""
    print("🚀 TECHRYPT PRODUCTION CHATBOT STARTUP")
    print(f"⏰ Started at: {datetime.now()}")
    print("=" * 80)
    
    # Step 1: Install dependencies
    install_dependencies()
    
    # Step 2: Download LLM models
    download_llm_models()
    
    # Step 3: Setup MongoDB
    mongodb_available = setup_mongodb()
    
    # Step 4: Start production server
    server_started = start_production_server()
    
    if server_started:
        print("\n🎉 PRODUCTION SYSTEM READY!")
        print("=" * 50)
        print("🌐 AI Server: http://localhost:5000")
        print("💬 Chat Endpoint: POST http://localhost:5000/chat")
        print("🔗 Health Check: http://localhost:5000/health")
        
        if mongodb_available:
            print("🗄️ MongoDB: Connected and ready")
        else:
            print("🗄️ MongoDB: Using in-memory storage")
        
        print("\n📊 FEATURES ENABLED:")
        print("✅ DialoGPT-medium LLM")
        print("✅ Intelligent Service Detection")
        print("✅ Business Type Recognition")
        print("✅ Appointment Scheduling")
        print("✅ Memory & Context")
        print("✅ Production-Ready Performance")
        
        # Step 5: Run tests
        run_comprehensive_tests()
        
        print("\n🎯 SYSTEM IS LIVE AND READY FOR THOUSANDS OF USERS!")
        print("🌍 Ready for global deployment!")
        
    else:
        print("\n❌ PRODUCTION STARTUP FAILED")
        print("Please check the logs and try again")

if __name__ == "__main__":
    main()
