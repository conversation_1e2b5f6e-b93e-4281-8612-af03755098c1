#!/usr/bin/env python3
"""
Test LLM vs Hardcoded Responses - Analyze Response Sources
"""

import requests
import json

def test_response_sources():
    """Test what type of responses the chatbot is using"""
    
    base_url = "http://localhost:5000"
    
    print("🤖 Testing LLM vs Hardcoded Response Sources")
    print("=" * 60)
    print("🧠 LLM = Dynamic AI-generated responses")
    print("📝 HARDCODED = Pre-written fixed responses")
    print("🔍 HYBRID = Mix of both")
    print("=" * 60)
    
    # Test cases to analyze response sources
    test_cases = [
        {"input": "hello", "description": "Greeting"},
        {"input": "services", "description": "Service list request"},
        {"input": "web scraping", "description": "Specific service request"},
        {"input": "copywriting", "description": "Copywriting service"},
        {"input": "I have a restaurant", "description": "Business type mention"},
        {"input": "shopify", "description": "Platform mention"},
        {"input": "what is digital marketing", "description": "General question"},
        {"input": "how can you help my business", "description": "Open-ended business question"},
        {"input": "tell me about automation", "description": "Service inquiry"},
        {"input": "I need help with social media", "description": "Social media request"},
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/{len(test_cases)}: '{test_case['input']}'")
        print(f"   Type: {test_case['description']}")
        
        try:
            # Send request without name to test clean detection
            payload = {"message": test_case['input'], "name": ""}
            response = requests.post(f"{base_url}/chat", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '')
                
                print(f"   Response: {ai_response[:100]}...")
                
                # Analyze response characteristics to determine source
                response_analysis = analyze_response_source(ai_response, test_case['input'])
                print(f"   Source: {response_analysis}")
                
            else:
                print(f"   ❌ ERROR - HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ ERROR - {e}")
        
        print("-" * 60)
    
    print(f"\n🎯 RESPONSE SOURCE ANALYSIS COMPLETE!")
    print("🧠 LLM = Dynamic, contextual, varied responses")
    print("📝 HARDCODED = Fixed templates, structured format")
    print("🔍 HYBRID = Combination of both approaches")

def analyze_response_source(response: str, user_input: str) -> str:
    """Analyze if response is LLM-generated or hardcoded"""
    
    # Indicators of hardcoded responses
    hardcoded_indicators = [
        # Structured service lists
        "1. Website Development\n2. Social Media Marketing\n3. Branding Services",
        "Here are our main services",
        "Would you like to schedule a consultation",
        
        # Fixed greeting patterns
        "Hello! Welcome to Techrypt.io",
        "I'm here to help you grow your business",
        
        # Structured business responses
        "For your restaurant business, we can help with:",
        "For your ecommerce business, we can help with:",
        "Great! For your",
        
        # Fixed service responses
        "Perfect! Our Website Development team includes",
        "Great question! Our Social Media Marketing team",
        "Perfect! Our Branding Services team",
        "Excellent! We specialize in chatbot development",
    ]
    
    # Indicators of LLM responses
    llm_indicators = [
        # More natural, varied language
        "Based on my understanding",
        "I can help you understand",
        "Let me explain",
        "From my knowledge",
        "According to",
        
        # Dynamic, contextual responses
        "That's a great question about",
        "I understand you're looking for",
        "Digital marketing encompasses",
        
        # Less structured, more conversational
        "absolutely", "definitely", "certainly",
        "various ways", "multiple approaches",
        "depending on", "it depends",
    ]
    
    # Check for hardcoded patterns
    hardcoded_score = 0
    for indicator in hardcoded_indicators:
        if indicator.lower() in response.lower():
            hardcoded_score += 2
    
    # Check for LLM patterns
    llm_score = 0
    for indicator in llm_indicators:
        if indicator.lower() in response.lower():
            llm_score += 1
    
    # Additional analysis
    # Hardcoded responses tend to be more structured
    if ("1." in response and "2." in response and "3." in response and 
        "4." in response and "5." in response and "6." in response):
        hardcoded_score += 3
    
    # LLM responses tend to be more varied and natural
    if len(response.split()) > 50 and hardcoded_score == 0:
        llm_score += 1
    
    # Determine source
    if hardcoded_score > llm_score + 2:
        return f"📝 HARDCODED (score: H{hardcoded_score} vs L{llm_score})"
    elif llm_score > hardcoded_score:
        return f"🧠 LLM (score: L{llm_score} vs H{hardcoded_score})"
    elif hardcoded_score > 0 and llm_score > 0:
        return f"🔍 HYBRID (score: H{hardcoded_score} + L{llm_score})"
    else:
        return f"❓ UNCLEAR (score: H{hardcoded_score} vs L{llm_score})"

if __name__ == "__main__":
    test_response_sources()
