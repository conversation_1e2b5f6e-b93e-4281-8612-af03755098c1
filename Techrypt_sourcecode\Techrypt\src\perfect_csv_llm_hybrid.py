#!/usr/bin/env python3
"""
🎯 PERFECT CSV-LLM HYBRID SYSTEM
Makes Enhanced CSV mode work like LLM with perfect alignment
Runs exhaustive testing until all test cases pass
"""

import requests
import json
import time
import random
from datetime import datetime

class PerfectCSVLLMHybrid:
    def __init__(self, server_url="http://localhost:5000"):
        self.server_url = server_url
        self.test_results = []
        
    def run_exhaustive_testing(self):
        """Run exhaustive testing like a real non-technical user"""
        
        print("🎯 PERFECT CSV-LLM HYBRID TESTING")
        print("Testing Enhanced CSV mode to work like LLM")
        print("=" * 80)
        
        # Real user test scenarios
        test_scenarios = [
            {
                'name': 'Cleaning Company Owner',
                'tests': [
                    ('hello', 'Should welcome with Techrypt branding'),
                    ('i run a cleaning company how can you help me', 'Should detect cleaning business and offer specific services'),
                    ('how long does it take for chatbot implementation', 'Should provide chatbot timeline info'),
                    ('sure', 'Should maintain context and offer consultation'),
                    ('what business i have?', 'Should remember cleaning company context'),
                    ('how will social media marketing help my cleaning business', 'Should give cleaning-specific SMM advice')
                ]
            },
            {
                'name': 'Pet Shop Owner',
                'tests': [
                    ('hi', 'Should welcome with Techrypt branding'),
                    ('i have pet shop how can you help me', 'Should detect pet business'),
                    ('what business i have?', 'Should remember pet shop context'),
                    ('smm', 'Should give pet-specific social media advice'),
                    ('how will social media marketing help my pet shop', 'Should provide detailed pet business SMM help')
                ]
            },
            {
                'name': 'Restaurant Owner',
                'tests': [
                    ('good morning', 'Should welcome professionally'),
                    ('i own a restaurant', 'Should detect restaurant business'),
                    ('i need website', 'Should offer restaurant-specific website features'),
                    ('what about social media', 'Should provide restaurant SMM strategy'),
                    ('how can instagram help my restaurant', 'Should give Instagram-specific restaurant advice')
                ]
            },
            {
                'name': 'Confused New Business Owner',
                'tests': [
                    ('hello', 'Should welcome and offer help'),
                    ('i dont know what i need', 'Should guide confused user'),
                    ('i have a small business', 'Should ask for more details'),
                    ('can you help me grow', 'Should offer growth services'),
                    ('what is smm', 'Should explain social media marketing')
                ]
            },
            {
                'name': 'Multi-Service Requester',
                'tests': [
                    ('hi there', 'Should welcome professionally'),
                    ('i need website and chatbot', 'Should handle multiple services'),
                    ('also branding', 'Should add branding to services'),
                    ('how much for everything', 'Should suggest consultation for pricing'),
                    ('can you do it all together', 'Should explain integrated solutions')
                ]
            }
        ]
        
        total_tests = 0
        passed_tests = 0
        
        for scenario in test_scenarios:
            print(f"\n🎭 Testing: {scenario['name']}")
            print("=" * 60)
            
            scenario_passed = 0
            scenario_total = len(scenario['tests'])
            
            for message, expected in scenario['tests']:
                total_tests += 1
                
                print(f"\n💬 Test: \"{message}\"")
                print(f"Expected: {expected}")
                
                try:
                    response = requests.post(
                        f"{self.server_url}/chat",
                        json={'message': message, 'user_name': 'testuser'},
                        timeout=10
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        ai_response = result.get('response', '')
                        
                        # Evaluate response quality
                        is_good = self.evaluate_response_quality(message, ai_response, expected)
                        
                        if is_good:
                            passed_tests += 1
                            scenario_passed += 1
                            print(f"✅ PASS: {ai_response[:100]}...")
                        else:
                            print(f"❌ FAIL: {ai_response[:100]}...")
                            print(f"Issues found in response")
                    else:
                        print(f"❌ Server Error: {response.status_code}")
                        
                except Exception as e:
                    print(f"❌ Connection Error: {e}")
                
                time.sleep(1)  # Realistic user pause
            
            scenario_score = (scenario_passed / scenario_total) * 100
            print(f"\n📊 {scenario['name']} Score: {scenario_passed}/{scenario_total} ({scenario_score:.1f}%)")
        
        overall_score = (passed_tests / total_tests) * 100
        
        print(f"\n🎯 OVERALL RESULTS:")
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Overall Score: {overall_score:.1f}%")
        
        if overall_score >= 80:
            print("🏆 EXCELLENT! System is production-ready!")
        elif overall_score >= 60:
            print("✅ GOOD! System is working well!")
        else:
            print("⚠️ NEEDS IMPROVEMENT! System needs more work!")
        
        return overall_score
    
    def evaluate_response_quality(self, user_message, ai_response, expected):
        """Evaluate if response meets quality standards"""
        response_lower = ai_response.lower()
        message_lower = user_message.lower()
        
        quality_checks = []
        
        # 1. Techrypt branding check
        if 'techrypt' in response_lower:
            quality_checks.append(True)
        else:
            quality_checks.append(False)
            print("❌ Missing Techrypt branding")
        
        # 2. Consultation offer check
        consultation_words = ['consultation', 'schedule', 'discuss', 'meeting']
        if any(word in response_lower for word in consultation_words):
            quality_checks.append(True)
        else:
            quality_checks.append(False)
            print("❌ Missing consultation offer")
        
        # 3. Business context check
        if 'cleaning' in message_lower and 'cleaning' in response_lower:
            quality_checks.append(True)
        elif 'pet' in message_lower and 'pet' in response_lower:
            quality_checks.append(True)
        elif 'restaurant' in message_lower and ('restaurant' in response_lower or 'food' in response_lower):
            quality_checks.append(True)
        elif 'business' in message_lower:
            quality_checks.append(True)  # Generic business context
        else:
            quality_checks.append(True)  # Not a business-specific message
        
        # 4. Helpful response check
        helpful_indicators = ['help', 'can', 'will', 'service', 'offer']
        if any(indicator in response_lower for indicator in helpful_indicators):
            quality_checks.append(True)
        else:
            quality_checks.append(False)
            print("❌ Response not helpful")
        
        # 5. Professional tone check
        if len(ai_response) > 20 and not any(bad_word in response_lower for bad_word in ['error', 'fail', 'problem']):
            quality_checks.append(True)
        else:
            quality_checks.append(False)
            print("❌ Response too short or contains errors")
        
        # Return True if at least 80% of checks pass
        passed_checks = sum(quality_checks)
        total_checks = len(quality_checks)
        score = (passed_checks / total_checks) * 100
        
        return score >= 80

def main():
    """Main testing function"""
    tester = PerfectCSVLLMHybrid()
    
    # Check server connectivity
    try:
        response = requests.get(f"{tester.server_url}/health", timeout=5)
        if response.status_code != 200:
            print(f"❌ Server not responding at {tester.server_url}")
            return
    except:
        print(f"❌ Cannot connect to server at {tester.server_url}")
        return
    
    print("✅ Server connectivity confirmed")
    
    # Run exhaustive testing
    score = tester.run_exhaustive_testing()
    
    print(f"\n🎯 FINAL ASSESSMENT:")
    if score >= 80:
        print("🚀 SYSTEM IS PRODUCTION READY!")
        print("✅ Enhanced CSV mode working perfectly like LLM!")
    else:
        print("🔧 SYSTEM NEEDS MORE IMPROVEMENTS")
        print("💡 Continue refining the Enhanced CSV mode")
    
    print(f"\n📄 Testing completed at {datetime.now()}")

if __name__ == "__main__":
    main()
