#!/usr/bin/env python3
"""
DEEP BUSINESS SCENARIOS TEST
Test comprehensive business vs service differentiation for non-technical users
"""

import requests
import json
import time

def deep_business_scenarios_test():
    """Test deep business scenarios like a complete non-technical user"""

    base_url = "http://localhost:5000"

    print("💼 DEEP BUSINESS SCENARIOS TEST")
    print("=" * 70)
    print("Testing as a complete non-technical user (no name provided)")
    print("Testing deep differentiation between services and business types")
    print("=" * 70)

    # Test categories for comprehensive business scenario testing
    test_categories = {
        'business_types': {
            'description': 'Different business types mentioned by users',
            'tests': []
        },
        'service_requests': {
            'description': 'Direct service requests',
            'tests': []
        },
        'platform_mentions': {
            'description': 'Business platforms and tools',
            'tests': []
        },
        'industry_specific': {
            'description': 'Industry-specific business scenarios',
            'tests': []
        },
        'mixed_scenarios': {
            'description': 'Complex mixed business/service scenarios',
            'tests': []
        }
    }

    # 1. BUSINESS TYPES (User owns/has a business)
    business_type_tests = [
        {"input": "I have a restaurant", "expected": "restaurant_business", "description": "Restaurant ownership"},
        {"input": "I own a bakery", "expected": "bakery_business", "description": "Bakery ownership"},
        {"input": "I run a clothing store", "expected": "clothing_business", "description": "Clothing store ownership"},
        {"input": "My salon business", "expected": "salon_business", "description": "Salon business ownership"},
        {"input": "I have an online store", "expected": "ecommerce_business", "description": "E-commerce business ownership"},
        {"input": "I own a gym", "expected": "gym_business", "description": "Gym business ownership"},
        {"input": "My construction company", "expected": "construction_business", "description": "Construction company ownership"},
        {"input": "I have a dental clinic", "expected": "dental_business", "description": "Dental clinic ownership"},
        {"input": "I run a consulting firm", "expected": "consulting_business", "description": "Consulting firm ownership"},
        {"input": "My photography business", "expected": "photography_business", "description": "Photography business ownership"},
    ]

    # 2. SERVICE REQUESTS (User wants specific services)
    service_request_tests = [
        {"input": "I need a website", "expected": "website_service", "description": "Website development request"},
        {"input": "Can you help with social media marketing", "expected": "social_media_service", "description": "Social media service request"},
        {"input": "I want logo design", "expected": "branding_service", "description": "Logo design service request"},
        {"input": "Need help with Instagram growth", "expected": "social_media_service", "description": "Instagram growth service"},
        {"input": "Can you build a chatbot", "expected": "chatbot_service", "description": "Chatbot development service"},
        {"input": "I need payment gateway integration", "expected": "payment_service", "description": "Payment gateway service"},
        {"input": "Help with business automation", "expected": "automation_service", "description": "Automation service request"},
        {"input": "I want SEO for my website", "expected": "website_service", "description": "SEO service request"},
        {"input": "Need graphic design work", "expected": "branding_service", "description": "Graphic design service"},
        {"input": "Can you do copywriting", "expected": "copywriting_service", "description": "Copywriting service request"},
    ]

    # 3. PLATFORM MENTIONS (User mentions specific platforms)
    platform_mention_tests = [
        {"input": "shopify", "expected": "ecommerce_platform", "description": "Shopify platform mention"},
        {"input": "I use WordPress", "expected": "cms_platform", "description": "WordPress platform mention"},
        {"input": "My Daraz store", "expected": "ecommerce_platform", "description": "Daraz platform mention"},
        {"input": "Instagram business account", "expected": "social_platform", "description": "Instagram business mention"},
        {"input": "Facebook ads", "expected": "advertising_platform", "description": "Facebook ads mention"},
        {"input": "Stripe payments", "expected": "payment_platform", "description": "Stripe platform mention"},
        {"input": "WooCommerce store", "expected": "ecommerce_platform", "description": "WooCommerce mention"},
        {"input": "Squarespace website", "expected": "cms_platform", "description": "Squarespace mention"},
        {"input": "PayPal integration", "expected": "payment_platform", "description": "PayPal mention"},
        {"input": "Mailchimp campaigns", "expected": "marketing_platform", "description": "Mailchimp mention"},
    ]

    # 4. INDUSTRY-SPECIFIC SCENARIOS
    industry_specific_tests = [
        {"input": "food delivery business", "expected": "food_industry", "description": "Food delivery industry"},
        {"input": "real estate agency", "expected": "real_estate_industry", "description": "Real estate industry"},
        {"input": "healthcare practice", "expected": "healthcare_industry", "description": "Healthcare industry"},
        {"input": "law firm", "expected": "legal_industry", "description": "Legal industry"},
        {"input": "fitness center", "expected": "fitness_industry", "description": "Fitness industry"},
        {"input": "beauty salon", "expected": "beauty_industry", "description": "Beauty industry"},
        {"input": "auto repair shop", "expected": "automotive_industry", "description": "Automotive industry"},
        {"input": "educational institute", "expected": "education_industry", "description": "Education industry"},
        {"input": "travel agency", "expected": "travel_industry", "description": "Travel industry"},
        {"input": "financial services", "expected": "finance_industry", "description": "Financial industry"},
    ]

    # 5. MIXED SCENARIOS (Complex business + service combinations)
    mixed_scenario_tests = [
        {"input": "I have a restaurant and need a website", "expected": "business_plus_service", "description": "Restaurant + website service"},
        {"input": "My clothing store needs social media marketing", "expected": "business_plus_service", "description": "Clothing store + social media"},
        {"input": "Bakery business looking for Instagram growth", "expected": "business_plus_service", "description": "Bakery + Instagram service"},
        {"input": "Dental clinic needs appointment booking system", "expected": "business_plus_service", "description": "Dental + booking system"},
        {"input": "Gym business wants payment gateway", "expected": "business_plus_service", "description": "Gym + payment gateway"},
        {"input": "Photography business needs portfolio website", "expected": "business_plus_service", "description": "Photography + portfolio"},
        {"input": "Consulting firm wants chatbot for clients", "expected": "business_plus_service", "description": "Consulting + chatbot"},
        {"input": "Online store needs better branding", "expected": "business_plus_service", "description": "E-commerce + branding"},
        {"input": "Salon business looking for automation", "expected": "business_plus_service", "description": "Salon + automation"},
        {"input": "Construction company needs lead generation", "expected": "business_plus_service", "description": "Construction + lead gen"},
    ]

    # Assign tests to categories
    test_categories['business_types']['tests'] = business_type_tests
    test_categories['service_requests']['tests'] = service_request_tests
    test_categories['platform_mentions']['tests'] = platform_mention_tests
    test_categories['industry_specific']['tests'] = industry_specific_tests
    test_categories['mixed_scenarios']['tests'] = mixed_scenario_tests

    # Run comprehensive tests
    overall_results = {}
    total_tests = 0
    total_passed = 0

    for category_name, category_data in test_categories.items():
        print(f"\n🔍 {category_name.upper().replace('_', ' ')}")
        print(f"📝 {category_data['description']}")
        print("-" * 60)

        category_passed = 0
        category_total = len(category_data['tests'])

        for i, test in enumerate(category_data['tests'], 1):
            print(f"\n🧪 Test {i}/{category_total}: '{test['input']}'")
            print(f"   Expected: {test['description']}")

            result = run_business_test(base_url, test['input'], test['expected'], test['description'])

            if result['passed']:
                category_passed += 1
                total_passed += 1
                status = "✅ PASS"
            else:
                status = "❌ FAIL"

            total_tests += 1

            print(f"   Result: {status}")
            print(f"   Response: {result['response'][:100]}...")
            print(f"   Analysis: {result['analysis']}")

            # Longer delay to avoid overwhelming the server
            time.sleep(1.0)

        category_score = (category_passed / category_total) * 100 if category_total > 0 else 0
        overall_results[category_name] = {
            'passed': category_passed,
            'total': category_total,
            'score': category_score
        }

        print(f"\n📊 {category_name.replace('_', ' ').title()} Score: {category_passed}/{category_total} ({category_score:.1f}%)")

    # Generate comprehensive report
    print("\n" + "=" * 70)
    print("🎯 DEEP BUSINESS SCENARIOS REPORT")
    print("=" * 70)

    overall_score = (total_passed / total_tests) * 100 if total_tests > 0 else 0
    print(f"📊 OVERALL SCORE: {total_passed}/{total_tests} ({overall_score:.1f}%)")

    for category_name, results in overall_results.items():
        score = results['score']
        status_icon = "✅" if score >= 80 else "⚠️" if score >= 60 else "❌"
        print(f"{status_icon} {category_name.replace('_', ' ').title()}: {results['passed']}/{results['total']} ({score:.1f}%)")

    # Final assessment
    print(f"\n🎯 BUSINESS DIFFERENTIATION ASSESSMENT:")
    if overall_score >= 90:
        print("✅ EXCELLENT - Perfect business vs service differentiation")
    elif overall_score >= 80:
        print("✅ GOOD - Strong business vs service differentiation")
    elif overall_score >= 70:
        print("⚠️ ACCEPTABLE - Adequate business vs service differentiation")
    else:
        print("❌ NEEDS IMPROVEMENT - Poor business vs service differentiation")

    return overall_score

def run_business_test(base_url, user_input, expected_type, description):
    """Run a single business scenario test"""
    try:
        start_time = time.time()
        # Test without name (like a non-technical user)
        payload = {"message": user_input, "name": ""}
        response = requests.post(f"{base_url}/chat", json=payload, timeout=10)
        response_time = time.time() - start_time

        if response.status_code == 200:
            result = response.json()
            ai_response = result.get('response', '')

            # Analyze response quality and business differentiation
            analysis = analyze_business_response(ai_response, expected_type, user_input)

            return {
                'passed': analysis['passed'],
                'response': ai_response,
                'analysis': analysis['reason'],
                'response_time': response_time
            }
        else:
            return {
                'passed': False,
                'response': f"HTTP {response.status_code}",
                'analysis': "Server error",
                'response_time': response_time
            }
    except Exception as e:
        return {
            'passed': False,
            'response': f"Error: {e}",
            'analysis': "Request failed",
            'response_time': 0
        }

def analyze_business_response(response, expected_type, user_input):
    """Analyze if response properly differentiates business vs service scenarios"""
    response_lower = response.lower()

    # Basic quality checks
    if len(response.strip()) < 20:
        return {'passed': False, 'reason': 'Response too short'}

    # Check for business relevance
    business_terms = ['techrypt', 'service', 'business', 'help', 'website', 'social', 'marketing']
    if not any(term in response_lower for term in business_terms):
        return {'passed': False, 'reason': 'Not business-relevant'}

    # Type-specific analysis
    if expected_type.endswith('_business'):
        # Should recognize business ownership and provide comprehensive services
        if 'for your' in response_lower and ('business' in response_lower or 'restaurant' in response_lower or 'store' in response_lower):
            return {'passed': True, 'reason': 'Correctly identified business ownership'}
        else:
            return {'passed': False, 'reason': 'Failed to recognize business ownership'}

    elif expected_type.endswith('_service'):
        # Should focus on specific service mentioned
        service_keywords = {
            'website_service': ['website', 'development', 'web'],
            'social_media_service': ['social media', 'instagram', 'facebook'],
            'branding_service': ['branding', 'logo', 'design'],
            'chatbot_service': ['chatbot', 'automation'],
            'payment_service': ['payment', 'gateway'],
            'automation_service': ['automation', 'process'],
            'copywriting_service': ['copywriting', 'content']
        }

        if expected_type in service_keywords:
            keywords = service_keywords[expected_type]
            if any(keyword in response_lower for keyword in keywords):
                return {'passed': True, 'reason': f'Correctly identified {expected_type}'}

        return {'passed': False, 'reason': f'Failed to identify {expected_type}'}

    elif expected_type.endswith('_platform'):
        # Should recognize platform and provide relevant services
        if 'platform' in response_lower or 'store' in response_lower or any(term in response_lower for term in ['shopify', 'wordpress', 'daraz']):
            return {'passed': True, 'reason': 'Correctly identified platform mention'}
        else:
            return {'passed': False, 'reason': 'Failed to recognize platform'}

    elif expected_type.endswith('_industry'):
        # Should recognize industry and provide industry-specific advice
        if 'business' in response_lower and ('help' in response_lower or 'service' in response_lower):
            return {'passed': True, 'reason': 'Correctly identified industry context'}
        else:
            return {'passed': False, 'reason': 'Failed to provide industry-specific response'}

    elif expected_type == 'business_plus_service':
        # Should recognize both business ownership and specific service need
        # Look for business-specific responses with service details
        business_indicators = ['for your', 'your business', 'restaurant business', 'retail business', 'salon business']
        service_indicators = ['website', 'social media', 'branding', 'chatbot', 'payment', 'booking', 'automation']

        has_business_context = any(indicator in response_lower for indicator in business_indicators)
        has_service_context = any(indicator in response_lower for indicator in service_indicators)

        if has_business_context and has_service_context and len(response) > 100:
            return {'passed': True, 'reason': 'Correctly identified business + service combination'}
        else:
            return {'passed': False, 'reason': 'Failed to handle business + service combination'}

    # Default: check for general business relevance
    return {'passed': True, 'reason': 'General business response provided'}

if __name__ == "__main__":
    score = deep_business_scenarios_test()
    print(f"\n🏁 Final Business Differentiation Score: {score:.1f}%")
