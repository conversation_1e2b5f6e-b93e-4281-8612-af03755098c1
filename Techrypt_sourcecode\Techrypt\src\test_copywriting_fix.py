#!/usr/bin/env python3
"""
Test Copywriting Service Detection Fix
"""

import requests
import json

def test_copywriting_fix():
    """Test if copywriting is correctly detected as a service, not a business platform"""
    
    base_url = "http://localhost:5000"
    
    print("✍️ Testing Copywriting Service Detection Fix")
    print("=" * 60)
    
    # Test cases for copywriting and other services
    test_cases = [
        {
            "input": "copywriting",
            "expected_type": "service",
            "expected_keywords": ["copywriting", "content creation", "branding services"],
            "should_not_contain": ["e-commerce platform", "copy business"]
        },
        {
            "input": "copy writing",
            "expected_type": "service", 
            "expected_keywords": ["copywriting", "content creation"],
            "should_not_contain": ["e-commerce platform", "copy business"]
        },
        {
            "input": "I need a copywriter",
            "expected_type": "service",
            "expected_keywords": ["copywriting", "content creation"],
            "should_not_contain": ["e-commerce platform", "copywriter business"]
        },
        {
            "input": "content writing services",
            "expected_type": "service",
            "expected_keywords": ["copywriting", "content creation"],
            "should_not_contain": ["e-commerce platform", "content business"]
        },
        {
            "input": "logo design",
            "expected_type": "service",
            "expected_keywords": ["logo", "branding", "design"],
            "should_not_contain": ["e-commerce platform", "logo business"]
        },
        {
            "input": "website development",
            "expected_type": "service",
            "expected_keywords": ["website development", "website creation"],
            "should_not_contain": ["e-commerce platform", "website business"]
        },
        {
            "input": "social media marketing",
            "expected_type": "service",
            "expected_keywords": ["social media marketing", "social media"],
            "should_not_contain": ["e-commerce platform", "social business"]
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/{len(test_cases)}: '{test_case['input']}'")
        
        try:
            payload = {"message": test_case['input'], "name": "Alex"}
            response = requests.post(f"{base_url}/chat", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '').lower()
                
                print(f"Response: {result.get('response', '')[:150]}...")
                
                # Check if it's correctly detected as a service
                is_service = any(keyword in ai_response for keyword in test_case['expected_keywords'])
                
                # Check if it incorrectly mentions business/platform
                is_incorrect = any(keyword in ai_response for keyword in test_case['should_not_contain'])
                
                if is_service and not is_incorrect:
                    print("✅ CORRECT - Detected as service")
                    status = "CORRECT"
                elif is_service and is_incorrect:
                    print("⚠️ PARTIAL - Detected as service but also mentions business/platform")
                    status = "PARTIAL"
                elif not is_service and is_incorrect:
                    print("❌ WRONG - Detected as business/platform instead of service")
                    status = "WRONG"
                else:
                    print("❓ UNCLEAR - Response unclear")
                    status = "UNCLEAR"
                
                results.append({
                    "input": test_case['input'],
                    "status": status,
                    "response": result.get('response', '')[:200],
                    "is_service": is_service,
                    "is_incorrect": is_incorrect
                })
                
            else:
                print(f"❌ ERROR - HTTP {response.status_code}")
                results.append({
                    "input": test_case['input'],
                    "status": "ERROR",
                    "response": f"HTTP {response.status_code}",
                    "is_service": False,
                    "is_incorrect": False
                })
                
        except Exception as e:
            print(f"❌ ERROR - {e}")
            results.append({
                "input": test_case['input'],
                "status": "ERROR",
                "response": str(e),
                "is_service": False,
                "is_incorrect": False
            })
        
        print("-" * 60)
    
    # Summary
    correct_count = sum(1 for r in results if r['status'] == 'CORRECT')
    partial_count = sum(1 for r in results if r['status'] == 'PARTIAL')
    wrong_count = sum(1 for r in results if r['status'] == 'WRONG')
    total_count = len(results)
    
    print(f"\n📊 COPYWRITING FIX RESULTS:")
    print("=" * 60)
    print(f"✅ Correct: {correct_count}/{total_count}")
    print(f"⚠️ Partial: {partial_count}/{total_count}")
    print(f"❌ Wrong: {wrong_count}/{total_count}")
    print(f"📈 Success Rate: {((correct_count + partial_count)/total_count)*100:.1f}%")
    
    if correct_count >= total_count * 0.8:  # 80% success rate
        print("\n🎉 EXCELLENT! Copywriting detection is working correctly!")
    elif correct_count >= total_count * 0.6:  # 60% success rate
        print("\n👍 GOOD! Most copywriting requests are handled correctly.")
    else:
        print("\n⚠️ NEEDS IMPROVEMENT! Copywriting detection needs more work.")
    
    print(f"\n📝 DETAILED RESULTS:")
    for result in results:
        status_emoji = {'CORRECT': '✅', 'PARTIAL': '⚠️', 'WRONG': '❌', 'UNCLEAR': '❓', 'ERROR': '💥'}
        emoji = status_emoji.get(result['status'], '❓')
        print(f"{emoji} {result['input']} → {result['status']}")
        if result['status'] in ['PARTIAL', 'WRONG']:
            print(f"   Response: {result['response']}")
    
    return results

if __name__ == "__main__":
    test_copywriting_fix()
