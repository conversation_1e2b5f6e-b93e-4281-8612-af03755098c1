#!/usr/bin/env python3
"""
Test Web Search + LLM Platform Detection for ANY Platform
"""

import requests
import json
import time

def test_web_search_platforms():
    """Test web search + LLM for unknown platforms"""
    
    base_url = "http://localhost:5000"
    
    # Test cases including completely unknown/new platforms
    test_cases = [
        # Known platforms (should use manual + web search)
        {"input": "shopify", "type": "Known Platform", "expected": "e-commerce"},
        {"input": "pinterest business", "type": "Known Platform", "expected": "social media"},
        
        # Less common platforms (should use web search)
        {"input": "gumroad", "type": "Unknown Platform", "expected": "web search analysis"},
        {"input": "teachable", "type": "Unknown Platform", "expected": "web search analysis"},
        {"input": "convertkit", "type": "Unknown Platform", "expected": "web search analysis"},
        {"input": "thinkific", "type": "Unknown Platform", "expected": "web search analysis"},
        {"input": "kajabi", "type": "Unknown Platform", "expected": "web search analysis"},
        
        # Completely new/random platforms
        {"input": "zoominfo", "type": "Unknown Platform", "expected": "web search analysis"},
        {"input": "lemlist", "type": "Unknown Platform", "expected": "web search analysis"},
        {"input": "typeform", "type": "Unknown Platform", "expected": "web search analysis"},
        
        # Business names that might be platforms
        {"input": "calendly", "type": "Unknown Platform", "expected": "web search analysis"},
        {"input": "loom", "type": "Unknown Platform", "expected": "web search analysis"},
        {"input": "notion", "type": "Known Platform", "expected": "cms"},
        
        # Test edge cases
        {"input": "randomplatform123", "type": "Fake Platform", "expected": "generic or error"},
        {"input": "nonexistentapp", "type": "Fake Platform", "expected": "generic or error"},
    ]
    
    print("🌐 Testing Web Search + LLM Platform Detection")
    print("=" * 80)
    print("Testing intelligent web search for ANY platform (known + unknown)...")
    print("=" * 80)
    
    web_search_count = 0
    platform_specific_count = 0
    total_count = len(test_cases)
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/{total_count}:")
        print(f"Platform: '{test_case['input']}'")
        print(f"Type: {test_case['type']}")
        print(f"Expected: {test_case['expected']}")
        
        try:
            payload = {"message": test_case['input'], "name": ""}
            response = requests.post(f"{base_url}/chat", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '')
                
                print(f"Response: {ai_response[:200]}...")
                
                # Check if web search was used (look for platform-specific response)
                response_lower = ai_response.lower()
                platform_name = test_case['input'].lower()
                
                # Check if response mentions the platform specifically
                platform_mentioned = platform_name in response_lower
                
                # Check if response is platform-specific (not generic)
                generic_phrases = [
                    "i'm here to help you with techrypt.io's digital marketing services",
                    "what type of business do you have",
                    "thank you for your message"
                ]
                is_generic = any(phrase in response_lower for phrase in generic_phrases)
                
                # Check for web search indicators
                web_search_indicators = [
                    "based on my research",
                    "i understand you're working with",
                    "great! i understand",
                    "great! i see you're",
                    platform_name + " is a"
                ]
                web_search_used = any(indicator in response_lower for indicator in web_search_indicators)
                
                # Scoring
                if platform_mentioned and web_search_used and not is_generic:
                    print("🌟 EXCELLENT - Web search + platform-specific response")
                    web_search_count += 1
                    platform_specific_count += 1
                    score = "EXCELLENT"
                elif platform_mentioned and not is_generic:
                    print("✅ GOOD - Platform-specific response")
                    platform_specific_count += 1
                    score = "GOOD"
                elif web_search_used and not is_generic:
                    print("⚠️ PARTIAL - Web search used but not fully specific")
                    web_search_count += 1
                    score = "PARTIAL"
                elif not is_generic:
                    print("🔶 BASIC - Non-generic but not platform-specific")
                    score = "BASIC"
                else:
                    print("❌ GENERIC - Generic response")
                    score = "GENERIC"
                
                results.append({
                    "platform": test_case['input'],
                    "type": test_case['type'],
                    "response": ai_response[:150] + "...",
                    "score": score,
                    "platform_mentioned": platform_mentioned,
                    "web_search_used": web_search_used,
                    "is_generic": is_generic
                })
                    
            else:
                print(f"❌ ERROR - HTTP {response.status_code}")
                results.append({
                    "platform": test_case['input'],
                    "type": test_case['type'],
                    "response": f"HTTP Error {response.status_code}",
                    "score": "ERROR",
                    "platform_mentioned": False,
                    "web_search_used": False,
                    "is_generic": True
                })
                
        except Exception as e:
            print(f"❌ ERROR - {e}")
            results.append({
                "platform": test_case['input'],
                "type": test_case['type'],
                "response": f"Exception: {e}",
                "score": "ERROR",
                "platform_mentioned": False,
                "web_search_used": False,
                "is_generic": True
            })
        
        print("-" * 80)
        time.sleep(2)  # Delay for web search
    
    # Final Results Summary
    print(f"\n🏆 WEB SEARCH + LLM RESULTS:")
    print("=" * 80)
    print(f"🌐 Web search responses: {web_search_count}/{total_count}")
    print(f"🎯 Platform-specific responses: {platform_specific_count}/{total_count}")
    print(f"📊 Web search success rate: {(web_search_count/total_count)*100:.1f}%")
    print(f"🔥 Platform-specific rate: {(platform_specific_count/total_count)*100:.1f}%")
    
    # Type breakdown
    print(f"\n📋 PLATFORM TYPE BREAKDOWN:")
    types = {}
    for result in results:
        ptype = result['type']
        if ptype not in types:
            types[ptype] = {'total': 0, 'web_search': 0, 'platform_specific': 0}
        types[ptype]['total'] += 1
        if result['web_search_used']:
            types[ptype]['web_search'] += 1
        if result['platform_mentioned']:
            types[ptype]['platform_specific'] += 1
    
    for ptype, stats in types.items():
        web_rate = (stats['web_search']/stats['total'])*100
        specific_rate = (stats['platform_specific']/stats['total'])*100
        print(f"  {ptype}: {stats['web_search']}/{stats['total']} web search ({web_rate:.1f}%), {stats['platform_specific']}/{stats['total']} platform-specific ({specific_rate:.1f}%)")
    
    # Detailed results
    print(f"\n📝 DETAILED RESULTS:")
    print("-" * 80)
    for result in results:
        status_emoji = {
            'EXCELLENT': '🌟',
            'GOOD': '✅', 
            'PARTIAL': '⚠️',
            'BASIC': '🔶',
            'GENERIC': '❌',
            'ERROR': '💥'
        }.get(result['score'], '❓')
        
        web_emoji = '🌐' if result['web_search_used'] else '📋'
        specific_emoji = '🎯' if result['platform_mentioned'] else '❌'
        
        print(f"{status_emoji} {result['platform']} ({result['type']}) - {result['score']} {web_emoji} {specific_emoji}")
        print(f"   Response: {result['response']}")
        print()
    
    # Overall assessment
    if web_search_count >= total_count * 0.6:  # 60% web search usage
        print("🎉 EXCELLENT! Web search + LLM working for unknown platforms!")
        if platform_specific_count >= total_count * 0.7:  # 70% platform-specific
            print("🚀 OUTSTANDING! Most responses are platform-specific via web search!")
    elif web_search_count >= total_count * 0.4:  # 40% web search usage
        print("⚠️ GOOD but web search could be used more frequently")
    else:
        print("❌ POOR - Web search not being used effectively")
    
    return results

if __name__ == "__main__":
    results = test_web_search_platforms()
    
    # Save results to file
    with open('web_search_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    print(f"\n💾 Results saved to web_search_test_results.json")
