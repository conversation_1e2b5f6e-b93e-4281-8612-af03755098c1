#!/usr/bin/env python3
"""
🧠 INTELLIGENT SERVICE DETECTION SYSTEM
Advanced AI system to detect user needs and map to Techrypt services
"""

import re
import logging
from typing import Dict, List, Tuple, Any
from datetime import datetime
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class IntelligentServiceDetector:
    def __init__(self):
        """Initialize the intelligent service detection system"""
        self.services = {
            'website_development': {
                'name': 'Website Development',
                'description': 'Custom websites, e-commerce, SEO optimization, mobile-responsive design',
                'keywords': [
                    'website', 'site', 'web', 'online presence', 'domain', 'hosting',
                    'ecommerce', 'e-commerce', 'online store', 'shop online', 'sell online',
                    'seo', 'search engine', 'google ranking', 'online visibility',
                    'responsive', 'mobile friendly', 'landing page', 'portfolio'
                ],
                'business_specific': {
                    'restaurant': 'Online menu, table reservations, food ordering system',
                    'retail': 'E-commerce store, product catalog, inventory management',
                    'service': 'Service booking, portfolio showcase, client testimonials',
                    'healthcare': 'Appointment booking, patient portal, service information'
                }
            },
            'social_media_marketing': {
                'name': 'Social Media Marketing',
                'description': 'Instagram, Facebook, LinkedIn growth and engagement strategies',
                'keywords': [
                    'social media', 'smm', 'instagram', 'facebook', 'linkedin', 'twitter',
                    'social', 'followers', 'engagement', 'posts', 'content',
                    'marketing', 'promotion', 'brand awareness', 'reach',
                    'likes', 'shares', 'viral', 'influencer', 'ads', 'campaigns'
                ],
                'business_specific': {
                    'restaurant': 'Food photography, customer reviews, local community engagement',
                    'retail': 'Product showcases, customer testimonials, seasonal campaigns',
                    'service': 'Before/after showcases, client testimonials, expertise demonstration',
                    'healthcare': 'Health tips, patient education, community trust building'
                }
            },
            'branding_services': {
                'name': 'Branding Services',
                'description': 'Logo design, brand identity, marketing materials, brand strategy',
                'keywords': [
                    'logo', 'brand', 'branding', 'identity', 'design', 'visual',
                    'colors', 'typography', 'style guide', 'brand book',
                    'business cards', 'letterhead', 'marketing materials',
                    'brand strategy', 'brand positioning', 'brand voice'
                ],
                'business_specific': {
                    'restaurant': 'Menu design, restaurant ambiance, food packaging branding',
                    'retail': 'Product packaging, store signage, brand consistency',
                    'service': 'Professional image, trust-building visuals, expertise positioning',
                    'healthcare': 'Trust and credibility focused branding, professional appearance'
                }
            },
            'chatbot_development': {
                'name': 'Chatbot Development',
                'description': '24/7 AI customer service, lead generation, appointment booking automation',
                'keywords': [
                    'chatbot', 'chat bot', 'ai assistant', 'automation', 'customer service',
                    'support', '24/7', 'instant response', 'lead generation',
                    'appointment booking', 'faq', 'help desk', 'virtual assistant'
                ],
                'business_specific': {
                    'restaurant': 'Table booking, menu inquiries, order taking, customer support',
                    'retail': 'Product inquiries, order tracking, customer support, recommendations',
                    'service': 'Appointment scheduling, service inquiries, quote requests',
                    'healthcare': 'Appointment booking, symptom checker, general inquiries'
                }
            },
            'automation_packages': {
                'name': 'Automation Packages',
                'description': 'Business process automation, workflow optimization, efficiency solutions',
                'keywords': [
                    'automation', 'workflow', 'process', 'efficiency', 'streamline',
                    'automatic', 'integration', 'crm', 'erp', 'management system',
                    'inventory', 'scheduling', 'reporting', 'analytics'
                ],
                'business_specific': {
                    'restaurant': 'Order management, inventory tracking, staff scheduling',
                    'retail': 'Inventory management, order processing, customer management',
                    'service': 'Client management, scheduling automation, invoice generation',
                    'healthcare': 'Patient management, appointment scheduling, billing automation'
                }
            },
            'payment_gateway': {
                'name': 'Payment Gateway Integration',
                'description': 'Secure online payments, Stripe, PayPal, multiple payment options',
                'keywords': [
                    'payment', 'gateway', 'stripe', 'paypal', 'checkout', 'transaction',
                    'secure payment', 'online payment', 'credit card', 'debit card',
                    'payment processing', 'billing', 'invoice', 'subscription'
                ],
                'business_specific': {
                    'restaurant': 'Online ordering payments, table booking deposits, delivery payments',
                    'retail': 'E-commerce payments, subscription billing, refund processing',
                    'service': 'Service payments, deposit collection, recurring billing',
                    'healthcare': 'Appointment payments, insurance billing, payment plans'
                }
            }
        }
        
        self.business_types = {
            'restaurant': ['restaurant', 'cafe', 'food', 'dining', 'eatery', 'bistro', 'diner'],
            'retail': ['shop', 'store', 'retail', 'boutique', 'market', 'selling', 'products', 'showroom', 'electronics', 'electronics showroom', 'electronics store', 'appliances', 'gadgets'],
            'service': ['service', 'consulting', 'agency', 'firm', 'professional'],
            'healthcare': ['clinic', 'hospital', 'medical', 'health', 'doctor', 'dental'],
            'fitness': ['gym', 'fitness', 'yoga', 'training', 'wellness', 'sports'],
            'beauty': ['salon', 'spa', 'beauty', 'cosmetic', 'hair', 'nail'],
            'education': ['school', 'education', 'training', 'course', 'academy'],
            'real_estate': ['real estate', 'property', 'housing', 'rental'],
            'automotive': ['car', 'auto', 'vehicle', 'garage', 'mechanic'],
            'cleaning': ['cleaning', 'janitorial', 'housekeeping', 'maintenance'],
            'electronics': ['electronics', 'showroom', 'electronics showroom', 'electronics store', 'appliances', 'gadgets', 'tech', 'technology', 'devices']
        }
    
    def detect_services_and_business(self, user_message: str, 
                                   conversation_history: List[Dict] = None) -> Dict[str, Any]:
        """
        Intelligently detect services needed and business type from user message
        """
        message_lower = user_message.lower()
        
        # Detect business type
        business_type = self._detect_business_type(message_lower, conversation_history)
        
        # Detect needed services
        detected_services = self._detect_services(message_lower)
        
        # Generate intelligent recommendations
        recommendations = self._generate_recommendations(
            detected_services, business_type, message_lower
        )
        
        # Determine intent
        intent = self._determine_intent(message_lower, detected_services)
        
        return {
            'business_type': business_type,
            'detected_services': detected_services,
            'recommendations': recommendations,
            'intent': intent,
            'confidence_score': self._calculate_confidence(detected_services, business_type),
            'response_type': self._determine_response_type(intent, detected_services)
        }
    
    def _detect_business_type(self, message: str, 
                            conversation_history: List[Dict] = None) -> str:
        """Detect business type from message and conversation history"""
        
        # Check conversation history first
        if conversation_history:
            for conv in conversation_history[-3:]:  # Last 3 messages
                for btype, keywords in self.business_types.items():
                    if any(keyword in conv.get('user_message', '').lower() for keyword in keywords):
                        return btype
        
        # Check current message
        for btype, keywords in self.business_types.items():
            if any(keyword in message for keyword in keywords):
                return btype
        
        return 'general'
    
    def _detect_services(self, message: str) -> List[Dict[str, Any]]:
        """Detect services mentioned in the message"""
        detected = []
        
        for service_id, service_data in self.services.items():
            confidence = 0
            matched_keywords = []
            
            for keyword in service_data['keywords']:
                if keyword in message:
                    confidence += 1
                    matched_keywords.append(keyword)
            
            if confidence > 0:
                detected.append({
                    'service_id': service_id,
                    'service_name': service_data['name'],
                    'confidence': confidence,
                    'matched_keywords': matched_keywords
                })
        
        # Sort by confidence
        detected.sort(key=lambda x: x['confidence'], reverse=True)
        return detected
    
    def _generate_recommendations(self, detected_services: List[Dict], 
                                business_type: str, message: str) -> List[Dict[str, Any]]:
        """Generate intelligent service recommendations"""
        recommendations = []
        
        if detected_services:
            # User mentioned specific services
            for service in detected_services[:3]:  # Top 3
                service_id = service['service_id']
                service_data = self.services[service_id]
                
                # Get business-specific description
                business_desc = service_data['business_specific'].get(
                    business_type, 
                    service_data['description']
                )
                
                recommendations.append({
                    'service_name': service_data['name'],
                    'description': business_desc,
                    'relevance': 'high',
                    'reason': f"Based on your mention of: {', '.join(service['matched_keywords'])}"
                })
        else:
            # No specific services mentioned, recommend based on business type
            if business_type != 'general':
                # Recommend top 3 services for this business type
                top_services = ['website_development', 'social_media_marketing', 'branding_services']
                
                for service_id in top_services:
                    service_data = self.services[service_id]
                    business_desc = service_data['business_specific'].get(
                        business_type,
                        service_data['description']
                    )
                    
                    recommendations.append({
                        'service_name': service_data['name'],
                        'description': business_desc,
                        'relevance': 'medium',
                        'reason': f"Recommended for {business_type} businesses"
                    })
            else:
                # General recommendations
                general_services = ['website_development', 'social_media_marketing']
                for service_id in general_services:
                    service_data = self.services[service_id]
                    recommendations.append({
                        'service_name': service_data['name'],
                        'description': service_data['description'],
                        'relevance': 'medium',
                        'reason': "Popular choice for growing businesses"
                    })
        
        return recommendations
    
    def _determine_intent(self, message: str, detected_services: List[Dict]) -> str:
        """Determine user intent from message"""
        
        # Appointment/consultation intent (more specific)
        appointment_keywords = [
            'schedule appointment', 'book appointment', 'schedule meeting', 'book meeting',
            'schedule consultation', 'book consultation', 'schedule call', 'book call',
            'when can we meet', 'when are you available', 'book a time', 'schedule time'
        ]

        # Only trigger if explicitly requesting to schedule/book
        if any(keyword in message for keyword in appointment_keywords):
            return 'schedule_appointment'

        # Check for explicit booking requests
        booking_patterns = [
            'i want to schedule', 'i want to book', 'i need to schedule', 'i need to book',
            'can we schedule', 'can we book', 'let\'s schedule', 'let\'s book'
        ]

        if any(pattern in message for pattern in booking_patterns):
            return 'schedule_appointment'
        
        # Pricing intent
        pricing_keywords = [
            'cost', 'price', 'pricing', 'how much', 'budget', 'quote',
            'estimate', 'fee', 'charge', 'expensive', 'cheap'
        ]
        
        if any(keyword in message for keyword in pricing_keywords):
            return 'pricing_inquiry'
        
        # Service information intent
        info_keywords = [
            'what', 'how', 'tell me', 'explain', 'information', 'details',
            'about', 'help', 'can you', 'do you'
        ]
        
        if any(keyword in message for keyword in info_keywords):
            return 'information_request'
        
        # Service request intent
        if detected_services:
            return 'service_request'
        
        return 'general_inquiry'
    
    def _calculate_confidence(self, detected_services: List[Dict], business_type: str) -> float:
        """Calculate confidence score for the detection"""
        score = 0.0
        
        # Business type confidence
        if business_type != 'general':
            score += 0.3
        
        # Service detection confidence
        if detected_services:
            max_service_confidence = max(s['confidence'] for s in detected_services)
            score += min(0.7, max_service_confidence * 0.1)
        
        return min(1.0, score)
    
    def _determine_response_type(self, intent: str, detected_services: List[Dict]) -> str:
        """Determine the type of response to generate"""
        
        if intent == 'schedule_appointment':
            return 'appointment_booking'
        elif intent == 'pricing_inquiry':
            return 'consultation_redirect'
        elif intent == 'service_request' and detected_services:
            return 'service_explanation'
        elif intent == 'information_request':
            return 'information_response'
        else:
            return 'general_help'

# Global instance
service_detector = IntelligentServiceDetector()

def detect_user_needs(message: str, conversation_history: List[Dict] = None) -> Dict[str, Any]:
    """Main function to detect user needs"""
    return service_detector.detect_services_and_business(message, conversation_history)
