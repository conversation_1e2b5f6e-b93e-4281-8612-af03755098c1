#!/usr/bin/env python3
"""
Test Frontend-Backend Connection
"""

import requests
import json

def test_connection():
    """Test if React frontend can connect to Flask backend"""
    
    base_url = "http://localhost:5000"
    
    print("🔗 Testing Frontend-Backend Connection")
    print("=" * 50)
    
    # Test 1: Health Check
    print("\n🏥 Test 1: Health Check")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            print("✅ Backend is healthy")
            print(f"   Response: {response.json()}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False
    
    # Test 2: Chat Endpoint
    print("\n💬 Test 2: Chat Endpoint")
    try:
        payload = {
            "message": "hello",
            "name": "Test User"
        }
        response = requests.post(f"{base_url}/chat", json=payload)
        if response.status_code == 200:
            result = response.json()
            print("✅ Chat endpoint working")
            print(f"   Request: {payload}")
            print(f"   Response: {result.get('response', '')[:100]}...")
        else:
            print(f"❌ Chat endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Chat endpoint error: {e}")
        return False
    
    # Test 3: CORS Headers
    print("\n🌐 Test 3: CORS Headers")
    try:
        response = requests.options(f"{base_url}/chat")
        cors_headers = response.headers.get('Access-Control-Allow-Origin', '')
        if cors_headers == '*':
            print("✅ CORS headers configured correctly")
        else:
            print(f"⚠️ CORS headers: {cors_headers}")
    except Exception as e:
        print(f"❌ CORS test error: {e}")
    
    # Test 4: Business Intelligence
    print("\n🧠 Test 4: Business Intelligence")
    test_cases = [
        {"message": "services", "expected": "services"},
        {"message": "shopify", "expected": "shopify"},
        {"message": "I have a restaurant", "expected": "restaurant"}
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        try:
            payload = {"message": test_case["message"], "name": ""}
            response = requests.post(f"{base_url}/chat", json=payload)
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '').lower()
                expected = test_case["expected"].lower()
                
                if expected in ai_response:
                    print(f"   ✅ Test {i}: '{test_case['message']}' → Contains '{expected}'")
                else:
                    print(f"   ⚠️ Test {i}: '{test_case['message']}' → Missing '{expected}'")
                    print(f"      Response: {result.get('response', '')[:100]}...")
            else:
                print(f"   ❌ Test {i}: HTTP {response.status_code}")
        except Exception as e:
            print(f"   ❌ Test {i}: {e}")
    
    print("\n🎉 Connection Test Complete!")
    print("\n📋 Next Steps:")
    print("1. Open http://localhost:5173 in your browser")
    print("2. Look for the chatbot icon on the right side")
    print("3. Click the chatbot to open it")
    print("4. Test with messages like 'hello', 'services', 'shopify'")
    print("5. Check browser console (F12) for any errors")
    
    return True

if __name__ == "__main__":
    test_connection()
