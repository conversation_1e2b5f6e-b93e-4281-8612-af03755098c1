import P1 from "../../assets/Images/animoca.png";
import P2 from "../../assets/Images/nexters.png";
import P3 from "../../assets/Images/pixonic.png";
import sensor from "../../assets/Images/sensorium.png";
import mask from "../../assets/svgs/mask.svg";
import mask1 from "../../assets/svgs/mask1.svg";
import jac from "../../assets/Images/jacqImg.jpg";
import feroze from "../../assets/Images/ferozeImg.jpg";
import Bhatt from "../../assets/Images/maheshImg.jpg";
import sanam from "../../assets/Images/sanam.jpg";
import urwa from "../../assets/Images/urwa.jpg";
import farhan from "../../assets/Images/farhan.jpg";
import bohemia from "../../assets/Images/bohemia.jpg";
import P4 from "../../assets/Images/ferozeImg.jpg";

// VERTICLE

import Yt from "../Images/youtube.png";
import Insta from "../Images/instagram.png";
import Tt from "../Images/tiktok.png";
import Telegram from "../Images/telegram.png";
import twitch from "../Images/twitch.png";
import snap from "../Images/snapchat.png";
import fbg from "../Images/facebookgaming.png";
import ap from "../Images/applepodcasts.png";
import { work1, work2, work3 } from "../mainImages";

export const sliderdata = [
  {
    maskImg: mask,
    logo: P1,
  },
  {
    maskImg: mask1,
    logo: P2,
  },
  {
    maskImg: mask,
    logo: P3,
  },
  {
    maskImg: mask1,
    logo: P4,
  },
  {
    maskImg: mask,
    logo: sensor,
  },
];

export const social = [
  {
    icon: Yt,
  },
  {
    icon: Insta,
  },
  {
    icon: Tt,
  },
  {
    icon: Telegram,
  },
  {
    icon: twitch,
  },
  {
    icon: snap,
  },
  {
    icon: fbg,
  },
  {
    icon: ap,
  },
];

export const verticals = [
  {
    text: "E-commerce",
  },
  {
    text: "market",
  },
  {
    text: "Education",
  },
  {
    text: "Travel",
  },
  {
    text: "Finance",
  },
  {
    text: "Utilities",
  },
  {
    text: "Social",
  },
  {
    text: "Health",
  },
  {
    text: "Food & Drinks",
  },
  {
    text: "Entertainment",
  },
  {
    text: "Delivery",
  },
  {
    text: "Marketing",
  },
];

export const data = [
  {
    Company: "Big Bite Burgers",
    Name: "Big Bite Challenge",
    Services: "Viral Marketing Campaign",
    GEO: "North America, Europe",
    Platform: "Instagram, TikTok",
    Image: work1,
    Vertical: "Food & Beverage",
    Results:
      "Our 360° marketing campaign for Big Bite Burgers generated a 300% sales increase through strategic influencer partnerships and user-generated content. The #BigBiteChallenge TikTok campaign achieved 18M+ views, with 32% engagement rate from our targeted Gen Z demographic. We deployed geo-targeted ads to 15 major cities, resulting in 47,200 store visits tracked through QR code redemptions.",
  },
  {
    Company: "Barbie Lashes",
    Name: "Pink Revolution",
    Services: "Beauty Influencer Activation",
    GEO: "Global (15 markets)",
    Platform: "TikTok, Instagram Reels",
    Image: work2,
    Vertical: "Cosmetics",
    Results:
      "The Barbie Lashes campaign dominated beauty conversations for 6 consecutive weeks, trending in 15 TikTok communities including #BeautyTok and #MakeupTransformations. We onboarded 87 micro-influencers (10K-100K followers) achieving 23:1 ROI. The pink-themed collection sold out within 72 hours, with our AR virtual try-on filter being used 420,000+ times pre-launch.",
  },
  {
    Company: "Keithson's Diner",
    Name: "Retro Relaunch",
    Services: "Brand Revitalization",
    GEO: "United States",
    Platform: "Meta, YouTube, Spotify",
    Image: work3,
    Vertical: "Restaurant",
    Results:
      "Our retro-futuristic rebranding for Keithson's Burgers increased Gen Z foot traffic by 180% quarter-over-quarter. The 80s-themed AR filters drove 58,700 social shares, while our limited-edition NFT burger tokens generated $240K in secondary market sales. The integrated campaign blended physical diner experiences with Web3 elements, earning coverage in 9 industry publications.",
  }
];
