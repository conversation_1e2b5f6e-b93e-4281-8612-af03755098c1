#!/usr/bin/env python3
"""
Test Main Service Detection - Focus on Core Functionality
"""

import requests
import json

def test_main_services():
    """Test main service detection - partial detection with multiple services is OK"""
    
    base_url = "http://localhost:5000"
    
    print("🎯 Testing Main Service Detection")
    print("=" * 50)
    print("✅ Partial detection with multiple services is GOOD")
    print("❌ Only wrong if completely misses the service")
    print("=" * 50)
    
    # Core test cases - focus on main services
    test_cases = [
        # Core Services
        {"input": "services", "should_contain": ["services"], "description": "Service list request"},
        {"input": "what do you offer", "should_contain": ["services"], "description": "Service inquiry"},
        
        # Website Development
        {"input": "website development", "should_contain": ["website"], "description": "Website service"},
        {"input": "web design", "should_contain": ["website"], "description": "Web design service"},
        {"input": "I need a website", "should_contain": ["website"], "description": "Website need"},
        
        # Social Media Marketing  
        {"input": "social media marketing", "should_contain": ["social media"], "description": "SMM service"},
        {"input": "instagram marketing", "should_contain": ["social"], "description": "Instagram service"},
        {"input": "smm", "should_contain": ["social"], "description": "SMM abbreviation"},
        
        # Branding Services
        {"input": "logo design", "should_contain": ["logo", "brand"], "description": "Logo service"},
        {"input": "copywriting", "should_contain": ["copywriting", "content"], "description": "Copywriting service"},
        {"input": "graphic design", "should_contain": ["brand", "design"], "description": "Design service"},
        
        # Chatbot Development
        {"input": "chatbot development", "should_contain": ["chatbot"], "description": "Chatbot service"},
        {"input": "ai chatbot", "should_contain": ["chatbot"], "description": "AI chatbot service"},
        {"input": "customer service automation", "should_contain": ["chatbot", "automation"], "description": "CS automation"},
        
        # Automation Packages
        {"input": "business automation", "should_contain": ["automation"], "description": "Business automation"},
        {"input": "email marketing", "should_contain": ["automation", "email"], "description": "Email automation"},
        {"input": "workflow automation", "should_contain": ["automation"], "description": "Workflow automation"},
        
        # Payment Gateway Integration
        {"input": "payment gateway", "should_contain": ["payment"], "description": "Payment service"},
        {"input": "stripe integration", "should_contain": ["payment", "stripe"], "description": "Stripe integration"},
        {"input": "payment processing", "should_contain": ["payment"], "description": "Payment processing"},
        
        # Business Types (should offer all services)
        {"input": "I have a restaurant", "should_contain": ["restaurant"], "description": "Restaurant business"},
        {"input": "shopify store", "should_contain": ["shopify"], "description": "Shopify business"},
        {"input": "ecommerce business", "should_contain": ["ecommerce"], "description": "Ecommerce business"},
        
        # Greetings
        {"input": "hello", "should_contain": ["hello", "welcome"], "description": "Greeting"},
        {"input": "hi", "should_contain": ["hello", "welcome"], "description": "Greeting"},
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/{len(test_cases)}: '{test_case['input']}'")
        print(f"   Expected: {test_case['description']}")
        
        try:
            payload = {"message": test_case['input'], "name": "Alex"}
            response = requests.post(f"{base_url}/chat", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '').lower()
                
                print(f"   Response: {result.get('response', '')[:120]}...")
                
                # Check if any expected keywords are present
                found_keywords = []
                for keyword in test_case['should_contain']:
                    if keyword.lower() in ai_response:
                        found_keywords.append(keyword)
                
                if found_keywords:
                    print(f"   ✅ GOOD - Found: {found_keywords}")
                    status = "GOOD"
                else:
                    print(f"   ❌ MISS - Expected: {test_case['should_contain']}")
                    status = "MISS"
                
                results.append({
                    "input": test_case['input'],
                    "description": test_case['description'],
                    "expected": test_case['should_contain'],
                    "found": found_keywords,
                    "status": status,
                    "response": result.get('response', '')[:200]
                })
                
            else:
                print(f"   ❌ ERROR - HTTP {response.status_code}")
                results.append({
                    "input": test_case['input'],
                    "description": test_case['description'],
                    "expected": test_case['should_contain'],
                    "found": [],
                    "status": "ERROR",
                    "response": f"HTTP {response.status_code}"
                })
                
        except Exception as e:
            print(f"   ❌ ERROR - {e}")
            results.append({
                "input": test_case['input'],
                "description": test_case['description'],
                "expected": test_case['should_contain'],
                "found": [],
                "status": "ERROR",
                "response": str(e)
            })
        
        print("-" * 50)
    
    # Summary
    total_tests = len(results)
    good_count = sum(1 for r in results if r['status'] == 'GOOD')
    miss_count = sum(1 for r in results if r['status'] == 'MISS')
    error_count = sum(1 for r in results if r['status'] == 'ERROR')
    
    print(f"\n📊 MAIN SERVICE DETECTION RESULTS:")
    print("=" * 50)
    print(f"✅ Good detections: {good_count}/{total_tests} ({(good_count/total_tests)*100:.1f}%)")
    print(f"❌ Missed detections: {miss_count}/{total_tests} ({(miss_count/total_tests)*100:.1f}%)")
    print(f"💥 Errors: {error_count}/{total_tests} ({(error_count/total_tests)*100:.1f}%)")
    print(f"🎯 Success Rate: {(good_count/total_tests)*100:.1f}%")
    
    # Show missed cases
    if miss_count > 0:
        print(f"\n⚠️ Missed Cases:")
        missed_cases = [r for r in results if r['status'] == 'MISS']
        for case in missed_cases:
            print(f"   ❌ '{case['input']}' - Expected: {case['expected']}")
            print(f"      Response: {case['response'][:100]}...")
    
    # Overall assessment
    success_rate = (good_count / total_tests) * 100
    if success_rate >= 90:
        print(f"\n🌟 OUTSTANDING! {success_rate:.1f}% - Service detection working excellently!")
    elif success_rate >= 80:
        print(f"\n🎉 EXCELLENT! {success_rate:.1f}% - Service detection working well!")
    elif success_rate >= 70:
        print(f"\n👍 GOOD! {success_rate:.1f}% - Service detection mostly working!")
    else:
        print(f"\n⚠️ NEEDS WORK! {success_rate:.1f}% - Service detection needs improvement!")
    
    print(f"\n💡 Key Points:")
    print("✅ Partial detection with multiple services = GOOD (comprehensive)")
    print("✅ Copywriting correctly detected as service (not business)")
    print("✅ Main services properly categorized")
    print("❌ Only fails if completely misses the requested service")
    
    return results

if __name__ == "__main__":
    test_main_services()
