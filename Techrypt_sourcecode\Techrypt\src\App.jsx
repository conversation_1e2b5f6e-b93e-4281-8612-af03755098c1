import Footer from "./components/Footer/Footer.jsx";
import Header from "./components/Header/Header.jsx";
import Main from "./pages/Main/main.jsx";
import Influence from "./pages/Influence/influence.jsx";
import Creative from "./pages/Creative/creative.jsx";
import Performance from "./pages/Performance/performance.jsx";
import Work from "./pages/Work/work.jsx";
import About from "./pages/About/About.jsx";
import EventCalendar from "./pages/EventCalendar/EventCalendar.jsx";
import MessageSidebar from "./components/MessageSidebar/MessageSidebar.jsx";
import shortLogo from "./assets/logo/shortLogo.png";

import {
  BrowserRouter as Router,
  Routes,
  Route,
  useLocation,
} from "react-router-dom";
import React, { useEffect, useState, useRef } from "react";
import "./App.css";
import { AnimatedLoader, Loader } from "./assets/mainImages.js";
import { ToastContainer } from "react-toastify";
import PrivacyPolicy from "./components/PrivacyPolicy/PrivacyPolicy.jsx";
import TermsConditions from "./components/TermsConditions/TermsConditions.jsx";
import Offers from "./pages/Offers/Offers.jsx";
import ContactForm from "./components/ContactForm/ContactForm.jsx";
import ContactPage from "./pages/ContactPage/ContactPage.jsx";

// Scroll to top on route change
const ScrollToTop = () => {
  const location = useLocation();
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location.pathname]);

  return null;
};

const AppContent = () => {
  const location = useLocation();
  const isFirstLoad = useRef(true);
  const [loader, setLoader] = useState(true);

  useEffect(() => {
    if (isFirstLoad.current) {
      // First page load
      setLoader(true);
      setTimeout(() => {
        setLoader(false);
        isFirstLoad.current = false;
      }, 3000);
    } else {
      // Subsequent navigation
      setLoader(true);
      setTimeout(() => {
        setLoader(false);
      }, 3000);
    }
  }, [location.pathname]);

  return loader ? (
    <div className="flex justify-center items-center fixed inset-0 bg-[#000] z-50">
      <img src={AnimatedLoader} alt="Loading..." className="w-44 h-44 animate-pulse" />
    </div>
  ) : (
    <>
      <ToastContainer toastClassName={"bg-[#121212] border-2 border-primary text-white"} progressClassName={"bg-primary"} />
      <ScrollToTop />
      <Header />
      <Routes>
        <Route path="/" element={<Main />} />
        <Route path="/Influence" element={<Influence />} />
        <Route path="/Performance" element={<Performance />} />
        <Route path="/Creative" element={<Creative />} />
        <Route path="/Work" element={<Work />} />
        <Route path="/About" element={<About />} />
        <Route path="/Calendar" element={<EventCalendar />} />
        <Route path="/PrivacyPolicy" element={<PrivacyPolicy />} />
        <Route path="/Terms&Conditions" element={<TermsConditions />} />
        <Route path="/Offers" element={<Offers />} />
        <Route path="/Contact" element={<ContactPage />} />

      </Routes>
      <MessageSidebar />
      <Footer />
    </>
  );
};

const App = () => (
  <Router>
    <AppContent />
  </Router>
);

export default App;
