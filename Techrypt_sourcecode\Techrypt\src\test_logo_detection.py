#!/usr/bin/env python3
"""
Test Logo Detection
"""

import requests
import json

def test_logo_detection():
    """Test if logo designing is correctly detected"""
    
    base_url = "http://localhost:5000"
    
    test_cases = [
        "logo designing",
        "logo design", 
        "I need a logo",
        "graphic design",
        "brand design",
        "I need help with my website",
        "services"
    ]
    
    print("🎨 Testing Logo/Branding Service Detection")
    print("=" * 60)
    
    for i, test_input in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/{len(test_cases)}:")
        print(f"Input: '{test_input}'")
        
        try:
            payload = {"message": test_input, "name": ""}
            response = requests.post(f"{base_url}/chat", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '')
                
                print(f"Response: {ai_response[:200]}...")
                
                # Check if branding service is detected
                if "branding services" in ai_response.lower() or "logo" in ai_response.lower():
                    print("✅ BRANDING/LOGO SERVICE DETECTED")
                elif "website development" in ai_response.lower():
                    print("✅ WEBSITE SERVICE DETECTED")
                elif "here are our main services" in ai_response.lower():
                    print("✅ SERVICE LIST PROVIDED")
                else:
                    print("⚠️ GENERIC RESPONSE")
                    
            else:
                print(f"❌ ERROR - HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ ERROR - {e}")
        
        print("-" * 60)

if __name__ == "__main__":
    test_logo_detection()
