#!/usr/bin/env python3
"""
Test Specific Cases That Were Failing
"""

import requests
import json

def test_specific_cases():
    """Test the specific cases that were generating absurd responses"""
    
    base_url = "http://localhost:5000"
    
    # Test cases that were failing
    test_cases = [
        {
            "input": "hello",
            "name": "",
            "expected": "proper greeting"
        },
        {
            "input": "daraz",
            "name": "",
            "expected": "should recognize Daraz as ecommerce platform with structured services"
        },
        {
            "input": "services",
            "name": "",
            "expected": "should list all 6 services in structured format"
        },
        {
            "input": "i need service",
            "name": "",
            "expected": "should provide service information"
        },
        {
            "input": "customer service",
            "name": "",
            "expected": "should handle customer service inquiry"
        }
    ]
    
    print("🧪 Testing Specific Cases That Were Failing")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 Test {i}/{len(test_cases)}:")
        print(f"Input: '{test_case['input']}'")
        print(f"Expected: {test_case['expected']}")
        
        try:
            # Send request
            payload = {
                "message": test_case['input'],
                "name": test_case['name']
            }
            
            response = requests.post(f"{base_url}/chat", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '')
                
                print(f"Response: {ai_response}")
                
                # Check if response is good (not absurd)
                is_good = (
                    len(ai_response) > 20 and  # Substantial response
                    'Customer:' not in ai_response and  # No repetitive nonsense
                    'hello hello hello' not in ai_response.lower() and  # No repetition
                    ('techrypt' in ai_response.lower() or 'service' in ai_response.lower())  # Relevant content
                )
                
                # Special checks for specific inputs
                if test_case['input'] == 'daraz':
                    is_good = ('daraz' in ai_response.lower() and ('1.' in ai_response or '2.' in ai_response))
                elif test_case['input'] == 'services':
                    is_good = ('1.' in ai_response and '2.' in ai_response and 'website' in ai_response.lower())
                
                if is_good:
                    print("✅ PASS - Response is intelligent and relevant")
                else:
                    print("❌ FAIL - Response is still absurd or irrelevant")
                    
            else:
                print(f"❌ FAIL - HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ FAIL - Error: {e}")
        
        print("-" * 60)

if __name__ == "__main__":
    test_specific_cases()
