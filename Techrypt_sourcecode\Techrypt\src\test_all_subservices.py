#!/usr/bin/env python3
"""
Comprehensive Test for ALL Sub-Services
Tests every possible sub-service within our 6 main service categories
"""

import requests
import json
import time

def test_all_subservices():
    """Test all possible sub-services that users might request"""
    
    base_url = "http://localhost:5000"
    
    print("🔍 Testing ALL Sub-Services - Comprehensive Service Detection")
    print("=" * 80)
    print("Testing every possible sub-service within our 6 main categories...")
    print("=" * 80)
    
    # Comprehensive test cases for ALL sub-services
    test_cases = [
        # 1. WEBSITE DEVELOPMENT Sub-Services
        {"input": "website development", "category": "Website Development", "expected": "website"},
        {"input": "web development", "category": "Website Development", "expected": "website"},
        {"input": "web design", "category": "Website Development", "expected": "website"},
        {"input": "website design", "category": "Website Development", "expected": "website"},
        {"input": "landing page", "category": "Website Development", "expected": "website"},
        {"input": "landing page design", "category": "Website Development", "expected": "website"},
        {"input": "ecommerce website", "category": "Website Development", "expected": "website"},
        {"input": "online store", "category": "Website Development", "expected": "website"},
        {"input": "portfolio website", "category": "Website Development", "expected": "website"},
        {"input": "business website", "category": "Website Development", "expected": "website"},
        {"input": "responsive design", "category": "Website Development", "expected": "website"},
        {"input": "mobile website", "category": "Website Development", "expected": "website"},
        {"input": "seo optimization", "category": "Website Development", "expected": "website"},
        {"input": "search engine optimization", "category": "Website Development", "expected": "website"},
        {"input": "web app development", "category": "Website Development", "expected": "website"},
        
        # 2. SOCIAL MEDIA MARKETING Sub-Services
        {"input": "social media marketing", "category": "Social Media Marketing", "expected": "social_media"},
        {"input": "smm", "category": "Social Media Marketing", "expected": "social_media"},
        {"input": "instagram marketing", "category": "Social Media Marketing", "expected": "social_media"},
        {"input": "facebook marketing", "category": "Social Media Marketing", "expected": "social_media"},
        {"input": "linkedin marketing", "category": "Social Media Marketing", "expected": "social_media"},
        {"input": "twitter marketing", "category": "Social Media Marketing", "expected": "social_media"},
        {"input": "tiktok marketing", "category": "Social Media Marketing", "expected": "social_media"},
        {"input": "youtube marketing", "category": "Social Media Marketing", "expected": "social_media"},
        {"input": "social media management", "category": "Social Media Marketing", "expected": "social_media"},
        {"input": "social media strategy", "category": "Social Media Marketing", "expected": "social_media"},
        {"input": "content strategy", "category": "Social Media Marketing", "expected": "social_media"},
        {"input": "social media growth", "category": "Social Media Marketing", "expected": "social_media"},
        {"input": "followers growth", "category": "Social Media Marketing", "expected": "social_media"},
        {"input": "engagement boost", "category": "Social Media Marketing", "expected": "social_media"},
        {"input": "influencer marketing", "category": "Social Media Marketing", "expected": "social_media"},
        
        # 3. BRANDING SERVICES Sub-Services
        {"input": "logo design", "category": "Branding Services", "expected": "branding"},
        {"input": "logo creation", "category": "Branding Services", "expected": "branding"},
        {"input": "brand identity", "category": "Branding Services", "expected": "branding"},
        {"input": "brand design", "category": "Branding Services", "expected": "branding"},
        {"input": "graphic design", "category": "Branding Services", "expected": "branding"},
        {"input": "visual identity", "category": "Branding Services", "expected": "branding"},
        {"input": "corporate identity", "category": "Branding Services", "expected": "branding"},
        {"input": "business cards", "category": "Branding Services", "expected": "branding"},
        {"input": "brochure design", "category": "Branding Services", "expected": "branding"},
        {"input": "flyer design", "category": "Branding Services", "expected": "branding"},
        {"input": "marketing materials", "category": "Branding Services", "expected": "branding"},
        {"input": "copywriting", "category": "Branding Services", "expected": "branding"},
        {"input": "content writing", "category": "Branding Services", "expected": "branding"},
        {"input": "copywriter", "category": "Branding Services", "expected": "branding"},
        {"input": "marketing copy", "category": "Branding Services", "expected": "branding"},
        {"input": "sales copy", "category": "Branding Services", "expected": "branding"},
        {"input": "website copy", "category": "Branding Services", "expected": "branding"},
        {"input": "product descriptions", "category": "Branding Services", "expected": "branding"},
        
        # 4. CHATBOT DEVELOPMENT Sub-Services
        {"input": "chatbot development", "category": "Chatbot Development", "expected": "chatbot"},
        {"input": "chatbot creation", "category": "Chatbot Development", "expected": "chatbot"},
        {"input": "ai chatbot", "category": "Chatbot Development", "expected": "chatbot"},
        {"input": "customer service bot", "category": "Chatbot Development", "expected": "chatbot"},
        {"input": "support bot", "category": "Chatbot Development", "expected": "chatbot"},
        {"input": "virtual assistant", "category": "Chatbot Development", "expected": "chatbot"},
        {"input": "conversational ai", "category": "Chatbot Development", "expected": "chatbot"},
        {"input": "automated chat", "category": "Chatbot Development", "expected": "chatbot"},
        {"input": "chat automation", "category": "Chatbot Development", "expected": "chatbot"},
        {"input": "customer support automation", "category": "Chatbot Development", "expected": "chatbot"},
        
        # 5. AUTOMATION PACKAGES Sub-Services
        {"input": "business automation", "category": "Automation Packages", "expected": "automation"},
        {"input": "workflow automation", "category": "Automation Packages", "expected": "automation"},
        {"input": "process automation", "category": "Automation Packages", "expected": "automation"},
        {"input": "marketing automation", "category": "Automation Packages", "expected": "automation"},
        {"input": "email automation", "category": "Automation Packages", "expected": "automation"},
        {"input": "email marketing", "category": "Automation Packages", "expected": "automation"},
        {"input": "crm integration", "category": "Automation Packages", "expected": "automation"},
        {"input": "crm automation", "category": "Automation Packages", "expected": "automation"},
        {"input": "automated workflows", "category": "Automation Packages", "expected": "automation"},
        {"input": "system integration", "category": "Automation Packages", "expected": "automation"},
        {"input": "business optimization", "category": "Automation Packages", "expected": "automation"},
        
        # 6. PAYMENT GATEWAY INTEGRATION Sub-Services
        {"input": "payment gateway", "category": "Payment Gateway Integration", "expected": "payment"},
        {"input": "payment integration", "category": "Payment Gateway Integration", "expected": "payment"},
        {"input": "payment processing", "category": "Payment Gateway Integration", "expected": "payment"},
        {"input": "stripe integration", "category": "Payment Gateway Integration", "expected": "payment"},
        {"input": "paypal integration", "category": "Payment Gateway Integration", "expected": "payment"},
        {"input": "online payments", "category": "Payment Gateway Integration", "expected": "payment"},
        {"input": "secure payments", "category": "Payment Gateway Integration", "expected": "payment"},
        {"input": "payment system", "category": "Payment Gateway Integration", "expected": "payment"},
        {"input": "checkout system", "category": "Payment Gateway Integration", "expected": "payment"},
        {"input": "billing system", "category": "Payment Gateway Integration", "expected": "payment"},
        {"input": "transaction processing", "category": "Payment Gateway Integration", "expected": "payment"},
    ]
    
    results = []
    category_stats = {}
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/{len(test_cases)}: '{test_case['input']}'")
        print(f"   Category: {test_case['category']}")
        
        try:
            payload = {"message": test_case['input'], "name": ""}
            response = requests.post(f"{base_url}/chat", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '').lower()
                
                print(f"   Response: {result.get('response', '')[:100]}...")
                
                # Check if correct service category is detected
                expected_keywords = {
                    'website': ['website development', 'website creation', 'web development', 'seo'],
                    'social_media': ['social media marketing', 'social media', 'social presence'],
                    'branding': ['branding services', 'logo', 'brand identity', 'copywriting', 'graphic design'],
                    'chatbot': ['chatbot development', 'chatbot', 'customer service automation'],
                    'automation': ['automation packages', 'automation', 'workflow', 'email marketing'],
                    'payment': ['payment gateway', 'payment', 'stripe', 'paypal', 'secure payments']
                }
                
                expected_service = test_case['expected']
                keywords = expected_keywords.get(expected_service, [])
                
                is_correct = any(keyword in ai_response for keyword in keywords)
                
                # Check for wrong categorization
                wrong_categories = []
                for service, service_keywords in expected_keywords.items():
                    if service != expected_service:
                        if any(keyword in ai_response for keyword in service_keywords):
                            wrong_categories.append(service)
                
                if is_correct and not wrong_categories:
                    print("   ✅ CORRECT - Proper service detection")
                    status = "CORRECT"
                elif is_correct and wrong_categories:
                    print(f"   ⚠️ PARTIAL - Correct but also mentions: {wrong_categories}")
                    status = "PARTIAL"
                elif not is_correct and wrong_categories:
                    print(f"   ❌ WRONG - Detected as: {wrong_categories}")
                    status = "WRONG"
                else:
                    print("   ❓ UNCLEAR - No clear service detection")
                    status = "UNCLEAR"
                
                # Track category statistics
                category = test_case['category']
                if category not in category_stats:
                    category_stats[category] = {'total': 0, 'correct': 0, 'partial': 0, 'wrong': 0}
                
                category_stats[category]['total'] += 1
                if status == 'CORRECT':
                    category_stats[category]['correct'] += 1
                elif status == 'PARTIAL':
                    category_stats[category]['partial'] += 1
                elif status == 'WRONG':
                    category_stats[category]['wrong'] += 1
                
                results.append({
                    "input": test_case['input'],
                    "category": test_case['category'],
                    "expected": test_case['expected'],
                    "status": status,
                    "response": result.get('response', '')[:150],
                    "is_correct": is_correct,
                    "wrong_categories": wrong_categories
                })
                
            else:
                print(f"   ❌ ERROR - HTTP {response.status_code}")
                results.append({
                    "input": test_case['input'],
                    "category": test_case['category'],
                    "expected": test_case['expected'],
                    "status": "ERROR",
                    "response": f"HTTP {response.status_code}",
                    "is_correct": False,
                    "wrong_categories": []
                })
                
        except Exception as e:
            print(f"   ❌ ERROR - {e}")
            results.append({
                "input": test_case['input'],
                "category": test_case['category'],
                "expected": test_case['expected'],
                "status": "ERROR",
                "response": str(e),
                "is_correct": False,
                "wrong_categories": []
            })
        
        print("-" * 80)
        time.sleep(0.5)  # Small delay to avoid overwhelming the system
    
    # Calculate overall statistics
    total_tests = len(results)
    correct_count = sum(1 for r in results if r['status'] == 'CORRECT')
    partial_count = sum(1 for r in results if r['status'] == 'PARTIAL')
    wrong_count = sum(1 for r in results if r['status'] == 'WRONG')
    error_count = sum(1 for r in results if r['status'] == 'ERROR')
    
    print(f"\n🏆 COMPREHENSIVE SUB-SERVICE TEST RESULTS:")
    print("=" * 80)
    print(f"📊 Overall Statistics:")
    print(f"   ✅ Correct: {correct_count}/{total_tests} ({(correct_count/total_tests)*100:.1f}%)")
    print(f"   ⚠️ Partial: {partial_count}/{total_tests} ({(partial_count/total_tests)*100:.1f}%)")
    print(f"   ❌ Wrong: {wrong_count}/{total_tests} ({(wrong_count/total_tests)*100:.1f}%)")
    print(f"   💥 Errors: {error_count}/{total_tests} ({(error_count/total_tests)*100:.1f}%)")
    print(f"   🎯 Success Rate: {((correct_count + partial_count)/total_tests)*100:.1f}%")
    
    print(f"\n📋 Category Breakdown:")
    for category, stats in category_stats.items():
        total = stats['total']
        correct = stats['correct']
        partial = stats['partial']
        wrong = stats['wrong']
        success_rate = ((correct + partial) / total) * 100 if total > 0 else 0
        
        print(f"   {category}:")
        print(f"      ✅ {correct}/{total} correct, ⚠️ {partial}/{total} partial, ❌ {wrong}/{total} wrong")
        print(f"      🎯 Success Rate: {success_rate:.1f}%")
    
    # Identify problem areas
    print(f"\n⚠️ Problem Areas (Wrong/Unclear responses):")
    problem_cases = [r for r in results if r['status'] in ['WRONG', 'UNCLEAR']]
    if problem_cases:
        for case in problem_cases:
            print(f"   ❌ '{case['input']}' ({case['category']}) → {case['status']}")
            if case['wrong_categories']:
                print(f"      Incorrectly detected as: {case['wrong_categories']}")
    else:
        print("   🎉 No major problems found!")
    
    # Overall assessment
    overall_success = ((correct_count + partial_count) / total_tests) * 100
    if overall_success >= 90:
        print(f"\n🌟 OUTSTANDING! {overall_success:.1f}% success rate - All sub-services working excellently!")
    elif overall_success >= 80:
        print(f"\n🎉 EXCELLENT! {overall_success:.1f}% success rate - Most sub-services working well!")
    elif overall_success >= 70:
        print(f"\n👍 GOOD! {overall_success:.1f}% success rate - Sub-services mostly working.")
    else:
        print(f"\n⚠️ NEEDS IMPROVEMENT! {overall_success:.1f}% success rate - Sub-service detection needs work.")
    
    # Save detailed results
    with open('subservice_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    print(f"\n💾 Detailed results saved to subservice_test_results.json")
    
    return results, category_stats

if __name__ == "__main__":
    results, stats = test_all_subservices()
