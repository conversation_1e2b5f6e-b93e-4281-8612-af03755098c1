#!/usr/bin/env python3
"""
🧪 EXHAUSTIVE TESTING SUITE
Complete testing pipeline for production readiness
"""

import requests
import time
import json
import sys
from datetime import datetime
import threading
import concurrent.futures

class ExhaustiveTester:
    def __init__(self):
        self.base_url = "http://localhost:5000"
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        self.test_results = []
        
    def log_test(self, test_name, status, details="", score=0):
        """Log test result"""
        self.total_tests += 1
        if status == "PASS":
            self.passed_tests += 1
        else:
            self.failed_tests += 1
            
        result = {
            "test": test_name,
            "status": status,
            "details": details,
            "score": score,
            "timestamp": datetime.now().strftime("%H:%M:%S")
        }
        self.test_results.append(result)
        
        status_icon = "✅" if status == "PASS" else "❌"
        print(f"{status_icon} {test_name}: {status} ({score}%) - {details}")
        
    def test_server_health(self):
        """Test server connectivity and health"""
        print("\n🔍 TESTING SERVER HEALTH")
        print("=" * 50)
        
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            if response.status_code == 200:
                self.log_test("Server Health", "PASS", "Server responding", 100)
                return True
            else:
                self.log_test("Server Health", "FAIL", f"Status: {response.status_code}", 0)
                return False
        except Exception as e:
            self.log_test("Server Health", "FAIL", f"Connection error: {e}", 0)
            return False
    
    def test_llm_functionality(self):
        """Test LLM model functionality"""
        print("\n🤖 TESTING LLM FUNCTIONALITY")
        print("=" * 50)
        
        llm_tests = [
            ("Hello", "Basic greeting"),
            ("I need help with my business", "Business assistance"),
            ("What services do you offer?", "Service inquiry"),
            ("Can you help me with website development?", "Specific service request")
        ]
        
        for message, description in llm_tests:
            try:
                response = requests.post(
                    f"{self.base_url}/chat",
                    json={"message": message, "user_name": "TestUser"},
                    timeout=15
                )
                
                if response.status_code == 200:
                    result = response.json()
                    ai_response = result.get("response", "")
                    
                    # Score LLM response quality
                    score = 0
                    if len(ai_response) > 20:
                        score += 25
                    if "techrypt" in ai_response.lower():
                        score += 25
                    if any(word in ai_response.lower() for word in ["help", "can", "will", "offer"]):
                        score += 25
                    if any(word in ai_response.lower() for word in ["consultation", "schedule", "discuss"]):
                        score += 25
                    
                    if score >= 75:
                        self.log_test(f"LLM: {description}", "PASS", f"Quality response", score)
                    else:
                        self.log_test(f"LLM: {description}", "FAIL", f"Low quality response", score)
                else:
                    self.log_test(f"LLM: {description}", "FAIL", f"HTTP {response.status_code}", 0)
                    
            except Exception as e:
                self.log_test(f"LLM: {description}", "FAIL", f"Error: {e}", 0)
            
            time.sleep(1)
    
    def test_business_intelligence(self):
        """Test business type detection and service recommendations"""
        print("\n🏢 TESTING BUSINESS INTELLIGENCE")
        print("=" * 50)
        
        business_tests = [
            ("I run a cleaning company", "cleaning", ["website", "social media"]),
            ("I have a restaurant business", "restaurant", ["website", "social media"]),
            ("I own a pet shop", "pet", ["website", "social media"]),
            ("I operate a fitness gym", "fitness", ["website", "social media"]),
            ("I have an online store", "ecommerce", ["website", "payment"]),
            ("I run a dental clinic", "healthcare", ["website", "social media"])
        ]
        
        for message, business_type, expected_services in business_tests:
            try:
                response = requests.post(
                    f"{self.base_url}/chat",
                    json={"message": message, "user_name": "BusinessTest"},
                    timeout=15
                )
                
                if response.status_code == 200:
                    result = response.json()
                    ai_response = result.get("response", "").lower()
                    
                    # Check business detection
                    business_detected = business_type in ai_response or any(
                        word in ai_response for word in business_type.split()
                    )
                    
                    # Check service recommendations
                    services_mentioned = sum(1 for service in expected_services 
                                           if service in ai_response)
                    
                    score = 0
                    if business_detected:
                        score += 50
                    score += (services_mentioned / len(expected_services)) * 50
                    
                    if score >= 75:
                        self.log_test(f"Business: {business_type}", "PASS", 
                                    f"Detected & recommended services", int(score))
                    else:
                        self.log_test(f"Business: {business_type}", "FAIL", 
                                    f"Poor detection/recommendations", int(score))
                else:
                    self.log_test(f"Business: {business_type}", "FAIL", 
                                f"HTTP {response.status_code}", 0)
                    
            except Exception as e:
                self.log_test(f"Business: {business_type}", "FAIL", f"Error: {e}", 0)
            
            time.sleep(1)
    
    def test_service_detection(self):
        """Test service detection and mapping"""
        print("\n🛠️ TESTING SERVICE DETECTION")
        print("=" * 50)
        
        service_tests = [
            ("I need a website", ["website"]),
            ("Help with social media marketing", ["social media"]),
            ("I want branding services", ["branding"]),
            ("Need a chatbot for my business", ["chatbot"]),
            ("I need website and social media", ["website", "social media"]),
            ("Help with payment gateway integration", ["payment"]),
            ("I need automation for my business", ["automation"])
        ]
        
        for message, expected_services in service_tests:
            try:
                response = requests.post(
                    f"{self.base_url}/chat",
                    json={"message": message, "user_name": "ServiceTest"},
                    timeout=15
                )
                
                if response.status_code == 200:
                    result = response.json()
                    ai_response = result.get("response", "").lower()
                    
                    # Check service detection
                    services_detected = sum(1 for service in expected_services 
                                          if service in ai_response)
                    
                    score = (services_detected / len(expected_services)) * 100
                    
                    if score >= 80:
                        self.log_test(f"Service: {message[:30]}...", "PASS", 
                                    f"Detected {services_detected}/{len(expected_services)}", int(score))
                    else:
                        self.log_test(f"Service: {message[:30]}...", "FAIL", 
                                    f"Detected {services_detected}/{len(expected_services)}", int(score))
                else:
                    self.log_test(f"Service: {message[:30]}...", "FAIL", 
                                f"HTTP {response.status_code}", 0)
                    
            except Exception as e:
                self.log_test(f"Service: {message[:30]}...", "FAIL", f"Error: {e}", 0)
            
            time.sleep(1)
    
    def test_conversation_memory(self):
        """Test conversation context and memory"""
        print("\n🧠 TESTING CONVERSATION MEMORY")
        print("=" * 50)
        
        # Sequential conversation test
        conversation = [
            ("My name is John and I run a bakery", "Initial context"),
            ("What business do I have?", "Memory recall - business"),
            ("What's my name?", "Memory recall - name"),
            ("I need help with social media", "Service request with context")
        ]
        
        user_name = "MemoryTest"
        
        for i, (message, test_type) in enumerate(conversation):
            try:
                response = requests.post(
                    f"{self.base_url}/chat",
                    json={"message": message, "user_name": user_name},
                    timeout=15
                )
                
                if response.status_code == 200:
                    result = response.json()
                    ai_response = result.get("response", "").lower()
                    
                    score = 50  # Base score
                    
                    if i == 0:  # Initial context
                        if "john" in ai_response or "bakery" in ai_response:
                            score = 100
                    elif i == 1:  # Business recall
                        if "bakery" in ai_response:
                            score = 100
                    elif i == 2:  # Name recall
                        if "john" in ai_response:
                            score = 100
                    elif i == 3:  # Context + service
                        if "bakery" in ai_response and "social media" in ai_response:
                            score = 100
                    
                    if score >= 75:
                        self.log_test(f"Memory: {test_type}", "PASS", "Context maintained", score)
                    else:
                        self.log_test(f"Memory: {test_type}", "FAIL", "Context lost", score)
                else:
                    self.log_test(f"Memory: {test_type}", "FAIL", f"HTTP {response.status_code}", 0)
                    
            except Exception as e:
                self.log_test(f"Memory: {test_type}", "FAIL", f"Error: {e}", 0)
            
            time.sleep(1)
    
    def test_performance_load(self):
        """Test performance under load"""
        print("\n⚡ TESTING PERFORMANCE UNDER LOAD")
        print("=" * 50)
        
        def single_request():
            try:
                start_time = time.time()
                response = requests.post(
                    f"{self.base_url}/chat",
                    json={"message": "Hello test", "user_name": "LoadTest"},
                    timeout=10
                )
                end_time = time.time()
                
                if response.status_code == 200:
                    return end_time - start_time
                else:
                    return None
            except:
                return None
        
        # Test concurrent requests
        num_concurrent = 10
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_concurrent) as executor:
            futures = [executor.submit(single_request) for _ in range(num_concurrent)]
            response_times = [f.result() for f in concurrent.futures.as_completed(futures)]
        
        successful_requests = [t for t in response_times if t is not None]
        
        if len(successful_requests) >= 8:  # 80% success rate
            avg_time = sum(successful_requests) / len(successful_requests)
            if avg_time < 5.0:  # Under 5 seconds average
                self.log_test("Performance Load", "PASS", 
                            f"Avg: {avg_time:.2f}s, Success: {len(successful_requests)}/{num_concurrent}", 100)
            else:
                self.log_test("Performance Load", "FAIL", 
                            f"Slow response: {avg_time:.2f}s", 50)
        else:
            self.log_test("Performance Load", "FAIL", 
                        f"Low success rate: {len(successful_requests)}/{num_concurrent}", 25)
    
    def run_all_tests(self):
        """Run complete test suite"""
        print("🧪 EXHAUSTIVE TESTING SUITE STARTED")
        print(f"⏰ Started at: {datetime.now()}")
        print("=" * 80)
        
        # Test server health first
        if not self.test_server_health():
            print("❌ Server not responding - cannot continue testing")
            return False
        
        # Run all test categories
        self.test_llm_functionality()
        self.test_business_intelligence()
        self.test_service_detection()
        self.test_conversation_memory()
        self.test_performance_load()
        
        # Generate final report
        self.generate_report()
        
        return self.passed_tests > self.failed_tests
    
    def generate_report(self):
        """Generate comprehensive test report"""
        print(f"\n📊 EXHAUSTIVE TEST RESULTS")
        print("=" * 80)
        
        # Overall statistics
        success_rate = (self.passed_tests / self.total_tests) * 100 if self.total_tests > 0 else 0
        
        print(f"Total Tests: {self.total_tests}")
        print(f"Passed: {self.passed_tests}")
        print(f"Failed: {self.failed_tests}")
        print(f"Success Rate: {success_rate:.1f}%")
        
        # Category breakdown
        categories = {}
        for result in self.test_results:
            category = result["test"].split(":")[0]
            if category not in categories:
                categories[category] = {"pass": 0, "fail": 0, "total": 0, "avg_score": 0}
            
            categories[category]["total"] += 1
            categories[category]["avg_score"] += result["score"]
            
            if result["status"] == "PASS":
                categories[category]["pass"] += 1
            else:
                categories[category]["fail"] += 1
        
        print(f"\n📈 CATEGORY BREAKDOWN:")
        for category, stats in categories.items():
            avg_score = stats["avg_score"] / stats["total"] if stats["total"] > 0 else 0
            success_rate = (stats["pass"] / stats["total"]) * 100 if stats["total"] > 0 else 0
            print(f"  {category}: {stats['pass']}/{stats['total']} ({success_rate:.1f}%) - Avg Score: {avg_score:.1f}%")
        
        # Failed tests details
        failed_tests = [r for r in self.test_results if r["status"] == "FAIL"]
        if failed_tests:
            print(f"\n❌ FAILED TESTS DETAILS:")
            for test in failed_tests:
                print(f"  • {test['test']}: {test['details']}")
        
        # Overall assessment
        print(f"\n🎯 OVERALL ASSESSMENT:")
        if success_rate >= 90:
            print("🏆 EXCELLENT! System is production-ready")
        elif success_rate >= 80:
            print("✅ GOOD! System is mostly ready with minor issues")
        elif success_rate >= 70:
            print("⚠️ ACCEPTABLE! System needs some improvements")
        else:
            print("❌ POOR! System needs significant fixes")
        
        print(f"\n⏰ Completed at: {datetime.now()}")

def main():
    """Main testing function"""
    tester = ExhaustiveTester()
    
    print("🚀 Starting exhaustive testing...")
    print("Make sure AI server is running on http://localhost:5000")
    print("Waiting 3 seconds for server to be ready...")
    time.sleep(3)
    
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 TESTING COMPLETED SUCCESSFULLY!")
        print("System is ready for browser testing!")
    else:
        print("\n⚠️ TESTING REVEALED ISSUES!")
        print("Please fix issues before browser testing!")
    
    return success

if __name__ == "__main__":
    main()
