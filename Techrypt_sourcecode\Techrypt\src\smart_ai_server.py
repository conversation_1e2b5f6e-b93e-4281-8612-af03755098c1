#!/usr/bin/env python3
"""
Smart AI Server for Techrypt Chatbot
Clean, lightweight server using intelligent LLM backend
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import logging

# Import the smart AI backend
from smart_ai_backend import get_smart_ai_response, reset_smart_ai

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Flask app
app = Flask(__name__)
CORS(app)

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "service": "Smart Techrypt AI"}), 200

@app.route('/chat', methods=['POST'])
def chat():
    """Main chat endpoint using smart AI"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        user_message = data.get('message', '').strip()
        user_name = data.get('name', '').strip()
        
        if not user_message:
            return jsonify({"error": "No message provided"}), 400
        
        logger.info(f"Received message: {user_message} from user: {user_name}")
        
        # Get smart AI response
        ai_response = get_smart_ai_response(user_message, user_name)
        
        logger.info(f"Generated response: {ai_response[:100]}...")
        
        return jsonify({
            "response": ai_response,
            "status": "success"
        }), 200
        
    except Exception as e:
        logger.error(f"Error in chat endpoint: {e}")
        return jsonify({
            "error": "Internal server error",
            "response": "I apologize, but I'm having trouble processing your request. Please try again."
        }), 500

@app.route('/reset', methods=['POST'])
def reset():
    """Reset conversation context"""
    try:
        reset_smart_ai()
        logger.info("Conversation context reset")
        return jsonify({"status": "reset successful"}), 200
    except Exception as e:
        logger.error(f"Error resetting context: {e}")
        return jsonify({"error": "Reset failed"}), 500

if __name__ == '__main__':
    logger.info("Starting Smart Techrypt AI Server...")
    logger.info("Server will be available at: http://localhost:5000")
    
    # Run the server
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=False,
        threaded=True
    )
