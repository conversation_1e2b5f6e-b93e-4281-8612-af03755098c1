#!/usr/bin/env python3
"""
EXTENSIVE SERVICE REQUEST TESTING
Test all possible service scenarios to identify and fix issues
"""

import requests
import json
import time

def test_service_request(message, expected_keywords=None):
    """Test a specific service request"""
    try:
        print(f"\n🔍 Testing: '{message}'")
        
        response = requests.post(
            'http://localhost:5000/chat',
            json={'message': message, 'user_name': ''},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            ai_response = result.get('response', '')
            
            print(f"📝 Response ({len(ai_response)} chars): {ai_response[:200]}...")
            
            # Analyze response quality
            response_lower = ai_response.lower()
            
            # Check for business relevance
            business_terms = ['techrypt', 'service', 'business', 'help', 'website', 'social', 'marketing', 'brand']
            has_business_terms = any(term in response_lower for term in business_terms)
            
            # Check for specific service mentions
            service_terms = ['website development', 'social media', 'branding', 'chatbot', 'automation', 'payment']
            has_service_terms = any(term in response_lower for term in service_terms)
            
            # Check for consultation offer
            consultation_terms = ['consultation', 'schedule', 'discuss', 'book', 'appointment']
            has_consultation = any(term in response_lower for term in consultation_terms)
            
            # Check for expected keywords if provided
            has_expected = True
            if expected_keywords:
                has_expected = any(keyword.lower() in response_lower for keyword in expected_keywords)
            
            # Quality scoring
            quality_score = 0
            if has_business_terms: quality_score += 25
            if has_service_terms: quality_score += 25
            if has_consultation: quality_score += 25
            if has_expected: quality_score += 25
            
            status = "✅ PASS" if quality_score >= 75 else "❌ FAIL"
            print(f"📊 Quality Score: {quality_score}% - {status}")
            print(f"   Business Terms: {'✅' if has_business_terms else '❌'}")
            print(f"   Service Terms: {'✅' if has_service_terms else '❌'}")
            print(f"   Consultation: {'✅' if has_consultation else '❌'}")
            print(f"   Expected Keywords: {'✅' if has_expected else '❌'}")
            
            return quality_score >= 75, ai_response, quality_score
            
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False, "", 0
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False, "", 0

def main():
    print("🚀 EXTENSIVE SERVICE REQUEST TESTING")
    print("=" * 60)
    
    # Wait for server
    print("⏳ Waiting for AI server...")
    time.sleep(3)
    
    # Test cases for different service request types
    test_cases = [
        # Basic service requests
        ("website development", ["website", "development", "web"]),
        ("social media marketing", ["social", "media", "marketing"]),
        ("branding services", ["branding", "brand", "logo"]),
        ("chatbot development", ["chatbot", "chat", "automation"]),
        ("automation packages", ["automation", "workflow", "process"]),
        ("payment gateway", ["payment", "gateway", "stripe"]),
        
        # Service variations
        ("I need a website", ["website", "development"]),
        ("help with social media", ["social", "media"]),
        ("logo design", ["logo", "branding", "design"]),
        ("customer service automation", ["chatbot", "automation"]),
        ("payment processing", ["payment", "gateway"]),
        
        # Complex service requests
        ("web development and SEO", ["website", "seo", "development"]),
        ("social media growth strategy", ["social", "media", "growth"]),
        ("complete branding package", ["branding", "package", "logo"]),
        ("AI chatbot for customer support", ["chatbot", "ai", "customer"]),
        ("business process automation", ["automation", "business", "process"]),
        
        # Technical service requests
        ("web scraping services", ["web scraping", "data extraction"]),
        ("copywriting services", ["copywriting", "content", "writing"]),
        ("SEO optimization", ["seo", "optimization", "search"]),
        ("e-commerce development", ["ecommerce", "online store"]),
        
        # Vague service requests (challenging)
        ("digital marketing", ["digital", "marketing"]),
        ("online presence", ["online", "website", "social"]),
        ("business growth", ["business", "growth"]),
        ("marketing help", ["marketing", "help"]),
        
        # Service + business combinations
        ("restaurant website", ["restaurant", "website"]),
        ("salon social media", ["salon", "social media"]),
        ("clinic appointment system", ["clinic", "appointment", "booking"]),
        ("gym payment system", ["gym", "payment"]),
    ]
    
    passed = 0
    total = len(test_cases)
    detailed_results = []
    
    for message, expected_keywords in test_cases:
        success, response, score = test_service_request(message, expected_keywords)
        if success:
            passed += 1
        detailed_results.append((message, success, score, response))
        time.sleep(1)  # Rate limiting
    
    # Results summary
    print("\n" + "=" * 60)
    print("📊 EXTENSIVE TESTING RESULTS")
    print("=" * 60)
    print(f"✅ Passed: {passed}/{total} ({(passed/total)*100:.1f}%)")
    print(f"❌ Failed: {total-passed}/{total}")
    
    # Detailed failure analysis
    failures = [result for result in detailed_results if not result[1]]
    if failures:
        print(f"\n🔍 FAILURE ANALYSIS ({len(failures)} failures):")
        for message, success, score, response in failures:
            print(f"\n❌ FAILED: '{message}' (Score: {score}%)")
            print(f"   Response: {response[:150]}...")
    
    # Success analysis
    successes = [result for result in detailed_results if result[1]]
    if successes:
        print(f"\n✅ SUCCESS PATTERNS ({len(successes)} successes):")
        avg_score = sum(result[2] for result in successes) / len(successes)
        print(f"   Average Score: {avg_score:.1f}%")
    
    # Recommendations
    print(f"\n🎯 RECOMMENDATIONS:")
    if passed/total < 0.8:
        print("   🔧 CRITICAL: Service request handling needs major improvements")
        print("   📝 Focus on: Service-specific responses and keyword detection")
        print("   🎯 Target: 80%+ success rate for production readiness")
    else:
        print("   ✅ Service request handling is performing well")
        print("   🚀 Ready for production deployment")

if __name__ == "__main__":
    main()
