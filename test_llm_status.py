#!/usr/bin/env python3
"""
Test LLM Status and Functionality
"""

import requests
import json

def test_llm_status():
    """Test if LLM is loaded and working"""
    
    base_url = "http://localhost:5000"
    
    print("🤖 Testing LLM Status and Functionality")
    print("=" * 50)
    
    # Test LLM status
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            print("✅ Backend is healthy")
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Cannot connect to backend: {e}")
        return
    
    # Test simple LLM query
    test_cases = [
        {"input": "hello", "description": "Simple greeting - should use LLM"},
        {"input": "how can you help", "description": "General question - should use LLM"},
        {"input": "what is digital marketing", "description": "Business question - should use LLM"},
        {"input": "services", "description": "Service list - should use HARDCODED"},
        {"input": "web scraping", "description": "Specific service - should use HARDCODED"},
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/{len(test_cases)}: '{test_case['input']}'")
        print(f"   Expected: {test_case['description']}")
        
        try:
            payload = {"message": test_case['input'], "name": ""}
            response = requests.post(f"{base_url}/chat", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '')
                
                print(f"   Response: {ai_response[:100]}...")
                
                # Check response characteristics
                if "Here are our main services" in ai_response:
                    print("   Source: 📝 HARDCODED (service list)")
                elif "Perfect! Our" in ai_response and ("Website Development team" in ai_response or "Branding Services team" in ai_response):
                    print("   Source: 📝 HARDCODED (specific service)")
                elif "Great! I understand you're working with" in ai_response:
                    print("   Source: 🔍 WEB SEARCH (platform detection)")
                elif "Hello! Welcome to Techrypt.io" in ai_response:
                    print("   Source: 📝 HARDCODED (greeting)")
                else:
                    print("   Source: 🧠 LLM (dynamic response)")
                
            else:
                print(f"   ❌ ERROR - HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ ERROR - {e}")
        
        print("-" * 50)
    
    print(f"\n🎯 LLM STATUS TEST COMPLETE!")
    print("📝 HARDCODED = Fixed template responses")
    print("🧠 LLM = Dynamic AI-generated responses")
    print("🔍 WEB SEARCH = Platform detection responses")

if __name__ == "__main__":
    test_llm_status()
