#!/usr/bin/env python3
"""
Test LLM Usage vs Hardcoded Responses
"""

import requests
import json

def test_llm_vs_hardcoded():
    """Test which responses are coming from LLM vs hardcoded"""
    
    base_url = "http://localhost:5000"
    
    test_cases = [
        {
            "input": "hello there",
            "description": "Should trigger LLM conversation"
        },
        {
            "input": "how can you help me",
            "description": "Should trigger LLM conversation"
        },
        {
            "input": "tell me a joke",
            "description": "Should trigger LLM conversation"
        },
        {
            "input": "what do you think about AI",
            "description": "Should trigger LLM conversation"
        },
        {
            "input": "explain machine learning",
            "description": "Should trigger LLM or web search"
        },
        {
            "input": "services",
            "description": "Should trigger hardcoded response"
        },
        {
            "input": "daraz store",
            "description": "Should trigger hardcoded business platform"
        },
        {
            "input": "random conversation topic",
            "description": "Should trigger LLM conversation"
        }
    ]
    
    print("🤖 Testing LLM vs Hardcoded Response Detection")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/{len(test_cases)}:")
        print(f"Input: '{test_case['input']}'")
        print(f"Description: {test_case['description']}")
        
        try:
            payload = {"message": test_case['input'], "name": ""}
            response = requests.post(f"{base_url}/chat", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '')
                
                print(f"Response: {ai_response[:200]}...")
                
                # Analyze response characteristics
                is_structured = ('1.' in ai_response and '2.' in ai_response and '3.' in ai_response)
                is_business_platform = ('Great!' in ai_response and 'store' in ai_response.lower())
                is_service_list = ('Website Development' in ai_response and 'Social Media Marketing' in ai_response)
                is_safety_rejection = ('cannot assist' in ai_response.lower() or 'illegal' in ai_response.lower())
                
                # Determine response type
                if is_structured or is_business_platform or is_service_list or is_safety_rejection:
                    response_type = "🔧 HARDCODED"
                    print(f"Response Type: {response_type}")
                    if is_structured or is_service_list:
                        print("  → Structured service list")
                    elif is_business_platform:
                        print("  → Business platform detection")
                    elif is_safety_rejection:
                        print("  → Safety filtering")
                else:
                    response_type = "🤖 LLM/AI"
                    print(f"Response Type: {response_type}")
                    print("  → Natural language generation")
                
                # Check if response seems natural vs templated
                has_natural_language = any(word in ai_response.lower() for word in [
                    'i am', 'i can', 'i help', 'thank you', 'welcome', 'glad', 'happy'
                ])
                
                if has_natural_language and not is_structured:
                    print("  → Contains natural conversational elements")
                    
            else:
                print(f"❌ ERROR - HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ ERROR - {e}")
        
        print("-" * 60)

if __name__ == "__main__":
    test_llm_vs_hardcoded()
