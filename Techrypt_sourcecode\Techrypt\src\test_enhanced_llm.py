#!/usr/bin/env python3
"""
Test Enhanced LLM Main Engine
"""

import requests
import json

def test_enhanced_llm():
    """Test if enhanced LLM is working as main engine"""
    
    base_url = "http://localhost:5000"
    
    test_cases = [
        "services",
        "I need help with my website", 
        "how to grow Instagram followers",
        "I need a logo",
        "hello",
        "tell me a joke"
    ]
    
    print("🤖 Testing Enhanced LLM Main Engine")
    print("=" * 60)
    
    for i, test_input in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/{len(test_cases)}:")
        print(f"Input: '{test_input}'")
        
        try:
            payload = {"message": test_input, "name": ""}
            response = requests.post(f"{base_url}/chat", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '')
                
                print(f"Response: {ai_response[:150]}...")
                print("✅ SUCCESS")
                    
            else:
                print(f"❌ ERROR - HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ ERROR - {e}")
        
        print("-" * 60)

if __name__ == "__main__":
    test_enhanced_llm()
