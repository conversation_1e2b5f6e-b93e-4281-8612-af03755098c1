#!/usr/bin/env python3
"""
Test Random Unknown Businesses - Web Search Intelligence
"""

import requests
import json
import time

def test_random_businesses():
    """Test web search + AI analysis for completely random, unknown businesses"""
    
    base_url = "http://localhost:5000"
    
    # Test cases with completely random, unknown businesses
    test_cases = [
        # Completely Random Business Names
        {"input": "zoominfo", "type": "Unknown Business", "expected": "web search analysis"},
        {"input": "lemlist", "type": "Unknown Business", "expected": "web search analysis"},
        {"input": "typeform", "type": "Unknown Business", "expected": "web search analysis"},
        {"input": "calendly", "type": "Unknown Business", "expected": "web search analysis"},
        {"input": "loom", "type": "Unknown Business", "expected": "web search analysis"},
        {"input": "airtable", "type": "Unknown Business", "expected": "web search analysis"},
        {"input": "clickfunnels", "type": "Unknown Business", "expected": "web search analysis"},
        {"input": "convertkit", "type": "Unknown Business", "expected": "web search analysis"},
        {"input": "mailerlite", "type": "Unknown Business", "expected": "web search analysis"},
        {"input": "gumroad", "type": "Unknown Business", "expected": "web search analysis"},
        
        # Random Local Business Names
        {"input": "joe's pizza", "type": "Random Local", "expected": "web search analysis"},
        {"input": "smith automotive", "type": "Random Local", "expected": "web search analysis"},
        {"input": "green valley spa", "type": "Random Local", "expected": "web search analysis"},
        {"input": "blue moon cafe", "type": "Random Local", "expected": "web search analysis"},
        {"input": "tech solutions inc", "type": "Random Local", "expected": "web search analysis"},
        
        # Made-up Business Names
        {"input": "biztech solutions", "type": "Made-up", "expected": "web search analysis"},
        {"input": "digital nexus", "type": "Made-up", "expected": "web search analysis"},
        {"input": "creative minds agency", "type": "Made-up", "expected": "web search analysis"},
        {"input": "innovate labs", "type": "Made-up", "expected": "web search analysis"},
        {"input": "future works", "type": "Made-up", "expected": "web search analysis"},
        
        # Random Industry Terms
        {"input": "fintech startup", "type": "Industry Term", "expected": "web search analysis"},
        {"input": "saas platform", "type": "Industry Term", "expected": "web search analysis"},
        {"input": "blockchain company", "type": "Industry Term", "expected": "web search analysis"},
        {"input": "ai consulting", "type": "Industry Term", "expected": "web search analysis"},
        {"input": "digital marketing agency", "type": "Industry Term", "expected": "web search analysis"},
    ]
    
    print("🎲 Testing Random Unknown Businesses - Web Search Intelligence")
    print("=" * 80)
    print("Testing if web search works for ANY business, not just known ones...")
    print("=" * 80)
    
    web_search_count = 0
    intelligent_response_count = 0
    generic_response_count = 0
    total_count = len(test_cases)
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/{total_count}:")
        print(f"Random Business: '{test_case['input']}'")
        print(f"Type: {test_case['type']}")
        
        try:
            payload = {"message": test_case['input'], "name": ""}
            response = requests.post(f"{base_url}/chat", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '')
                
                print(f"Response: {ai_response[:200]}...")
                
                # Analyze response characteristics
                response_lower = ai_response.lower()
                input_name = test_case['input'].lower()
                
                # Check if business name is mentioned
                business_mentioned = input_name in response_lower
                
                # Check for web search indicators
                web_search_indicators = [
                    "based on my research",
                    "i understand you're working with",
                    "great! i understand",
                    f"{input_name} is a",
                    f"{input_name} is an"
                ]
                web_search_used = any(indicator in response_lower for indicator in web_search_indicators)
                
                # Check for intelligent business analysis
                business_analysis = [
                    "restaurant business", "e-commerce business", "service business", "tech business",
                    "platform", "software", "application", "service", "company"
                ]
                intelligent_analysis = any(analysis in response_lower for analysis in business_analysis)
                
                # Check for generic fallback responses
                generic_patterns = [
                    "i'm here to help you with techrypt.io's digital marketing services",
                    "what type of business do you have",
                    "thank you for your message"
                ]
                is_generic = any(pattern in response_lower for pattern in generic_patterns)
                
                # Check for service recommendations
                service_recommendations = [
                    "website development", "social media marketing", "branding services",
                    "chatbot development", "automation packages", "payment gateway"
                ]
                has_services = any(service in response_lower for service in service_recommendations)
                
                # Scoring
                if web_search_used and business_mentioned and intelligent_analysis and has_services:
                    print("🌟 EXCELLENT - Web search + intelligent business analysis")
                    web_search_count += 1
                    intelligent_response_count += 1
                    score = "EXCELLENT"
                elif business_mentioned and intelligent_analysis and has_services:
                    print("✅ GOOD - Intelligent business response")
                    intelligent_response_count += 1
                    score = "GOOD"
                elif web_search_used and business_mentioned:
                    print("⚠️ PARTIAL - Web search used but limited analysis")
                    web_search_count += 1
                    score = "PARTIAL"
                elif business_mentioned and has_services:
                    print("🔶 BASIC - Business mentioned with services")
                    score = "BASIC"
                elif is_generic:
                    print("❌ GENERIC - Generic fallback response")
                    generic_response_count += 1
                    score = "GENERIC"
                else:
                    print("❓ UNCLEAR - Unclear response type")
                    score = "UNCLEAR"
                
                results.append({
                    "business": test_case['input'],
                    "type": test_case['type'],
                    "response": ai_response[:150] + "...",
                    "score": score,
                    "business_mentioned": business_mentioned,
                    "web_search_used": web_search_used,
                    "intelligent_analysis": intelligent_analysis,
                    "has_services": has_services,
                    "is_generic": is_generic
                })
                    
            else:
                print(f"❌ ERROR - HTTP {response.status_code}")
                results.append({
                    "business": test_case['input'],
                    "type": test_case['type'],
                    "response": f"HTTP Error {response.status_code}",
                    "score": "ERROR",
                    "business_mentioned": False,
                    "web_search_used": False,
                    "intelligent_analysis": False,
                    "has_services": False,
                    "is_generic": False
                })
                
        except Exception as e:
            print(f"❌ ERROR - {e}")
            results.append({
                "business": test_case['input'],
                "type": test_case['type'],
                "response": f"Exception: {e}",
                "score": "ERROR",
                "business_mentioned": False,
                "web_search_used": False,
                "intelligent_analysis": False,
                "has_services": False,
                "is_generic": False
            })
        
        print("-" * 80)
        time.sleep(1)  # Small delay to avoid overwhelming the system
    
    # Final Results Summary
    print(f"\n🏆 RANDOM BUSINESS INTELLIGENCE RESULTS:")
    print("=" * 80)
    print(f"🌐 Web search responses: {web_search_count}/{total_count}")
    print(f"🧠 Intelligent responses: {intelligent_response_count}/{total_count}")
    print(f"❌ Generic responses: {generic_response_count}/{total_count}")
    print(f"📊 Web search success rate: {(web_search_count/total_count)*100:.1f}%")
    print(f"🔥 Intelligence success rate: {(intelligent_response_count/total_count)*100:.1f}%")
    print(f"⚠️ Generic fallback rate: {(generic_response_count/total_count)*100:.1f}%")
    
    # Business type breakdown
    print(f"\n📋 BUSINESS TYPE BREAKDOWN:")
    types = {}
    for result in results:
        btype = result['type']
        if btype not in types:
            types[btype] = {'total': 0, 'web_search': 0, 'intelligent': 0, 'generic': 0}
        types[btype]['total'] += 1
        if result['web_search_used']:
            types[btype]['web_search'] += 1
        if result['intelligent_analysis']:
            types[btype]['intelligent'] += 1
        if result['is_generic']:
            types[btype]['generic'] += 1
    
    for btype, stats in types.items():
        web_rate = (stats['web_search']/stats['total'])*100
        intelligent_rate = (stats['intelligent']/stats['total'])*100
        generic_rate = (stats['generic']/stats['total'])*100
        print(f"  {btype}: Web {stats['web_search']}/{stats['total']} ({web_rate:.1f}%), Intelligent {stats['intelligent']}/{stats['total']} ({intelligent_rate:.1f}%), Generic {stats['generic']}/{stats['total']} ({generic_rate:.1f}%)")
    
    # Detailed results
    print(f"\n📝 DETAILED RESULTS:")
    print("-" * 80)
    for result in results:
        status_emoji = {
            'EXCELLENT': '🌟',
            'GOOD': '✅', 
            'PARTIAL': '⚠️',
            'BASIC': '🔶',
            'GENERIC': '❌',
            'UNCLEAR': '❓',
            'ERROR': '💥'
        }.get(result['score'], '❓')
        
        web_emoji = '🌐' if result['web_search_used'] else '📋'
        intelligent_emoji = '🧠' if result['intelligent_analysis'] else '❌'
        
        print(f"{status_emoji} {result['business']} ({result['type']}) - {result['score']} {web_emoji} {intelligent_emoji}")
        print(f"   Response: {result['response']}")
        print()
    
    # Overall assessment
    total_intelligent = web_search_count + intelligent_response_count
    if total_intelligent >= total_count * 0.7:  # 70% intelligent responses
        print("🎉 EXCELLENT! Web search working for random businesses!")
        if web_search_count >= total_count * 0.5:  # 50% web search usage
            print("🚀 OUTSTANDING! Web search intelligence is working!")
    elif total_intelligent >= total_count * 0.5:  # 50% intelligent responses
        print("⚠️ GOOD but web search could work better for unknown businesses")
    else:
        print("❌ POOR - Web search not working for random businesses")
    
    return results

if __name__ == "__main__":
    results = test_random_businesses()
    
    # Save results to file
    with open('random_business_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    print(f"\n💾 Results saved to random_business_results.json")
