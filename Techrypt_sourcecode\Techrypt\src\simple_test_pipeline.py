#!/usr/bin/env python3
"""
COMPREHENSIVE TEST PIPELINE
Tests LLM performance after download completion
"""

import time
import requests
import json
from datetime import datetime

class ComprehensiveTestPipeline:
    def __init__(self):
        self.server_url = "http://localhost:5000"
        
    def run_comprehensive_tests(self):
        """Run comprehensive testing suite"""
        print("\n=== COMPREHENSIVE LLM TESTING SUITE ===")
        print(f"Started at: {datetime.now()}")
        print("=" * 60)
        
        # Comprehensive test scenarios
        test_scenarios = [
            {
                'category': 'Business Detection & Context Memory',
                'tests': [
                    ('hello', 'Welcome with Techrypt branding'),
                    ('i run a cleaning company', 'Detect cleaning business'),
                    ('what business do i have?', 'Remember cleaning context'),
                    ('how can social media help my cleaning business', 'Business-specific SMM advice'),
                    ('what services do you offer for cleaning companies', 'Cleaning-focused services')
                ]
            },
            {
                'category': 'Multi-Business Intelligence',
                'tests': [
                    ('i have a pet shop', 'Detect pet business'),
                    ('what business do i have now?', 'Remember pet shop context'),
                    ('i own a restaurant too', 'Handle multiple businesses'),
                    ('help with my restaurant social media', 'Restaurant-specific advice'),
                    ('what about my pet shop instagram', 'Pet shop Instagram strategy')
                ]
            },
            {
                'category': 'Service Integration',
                'tests': [
                    ('i need a website', 'Website development offer'),
                    ('also need chatbot', 'Add chatbot to services'),
                    ('and branding services', 'Integrated service package'),
                    ('how much for everything', 'Consultation for pricing'),
                    ('can you do it all together', 'Explain integrated solutions')
                ]
            },
            {
                'category': 'Complex Business Scenarios',
                'tests': [
                    ('i have shopify store selling clothes', 'E-commerce + fashion business'),
                    ('need help with instagram marketing', 'Fashion-specific Instagram'),
                    ('also want to add chatbot to shopify', 'E-commerce chatbot integration'),
                    ('how will automation help my online store', 'E-commerce automation'),
                    ('what about payment gateway integration', 'Payment solutions for e-commerce')
                ]
            },
            {
                'category': 'Error Handling & Edge Cases',
                'tests': [
                    ('mhy buisness nedds hlep', 'Handle typos and garbled text'),
                    ('i dont know what i need', 'Guide confused users'),
                    ('how much does everything cost', 'Redirect to consultation'),
                    ('can you build my website today', 'Manage unrealistic expectations'),
                    ('i need help with illegal business', 'Reject inappropriate requests')
                ]
            },
            {
                'category': 'Conversation Flow & Memory',
                'tests': [
                    ('hi there', 'Professional greeting'),
                    ('i run a gym', 'Detect fitness business'),
                    ('what did i just tell you', 'Demonstrate memory'),
                    ('how can smm help gyms', 'Fitness-specific SMM'),
                    ('what other services help fitness businesses', 'Comprehensive fitness solutions')
                ]
            }
        ]
        
        total_tests = 0
        passed_tests = 0
        category_scores = {}
        
        for scenario in test_scenarios:
            category = scenario['category']
            print(f"\n--- Testing Category: {category} ---")
            
            category_passed = 0
            category_total = len(scenario['tests'])
            
            for message, expected in scenario['tests']:
                total_tests += 1
                
                print(f"\nTest: \"{message}\"")
                print(f"Expected: {expected}")
                
                try:
                    response = requests.post(
                        f"{self.server_url}/chat",
                        json={'message': message, 'user_name': 'testuser'},
                        timeout=15
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        ai_response = result.get('response', '')
                        
                        # Comprehensive evaluation
                        score = self.evaluate_response_quality(message, ai_response, expected)
                        
                        if score >= 80:
                            passed_tests += 1
                            category_passed += 1
                            print(f"PASS ({score}%): {ai_response[:80]}...")
                        else:
                            print(f"FAIL ({score}%): {ai_response[:80]}...")
                            self.analyze_failure(message, ai_response, expected)
                    else:
                        print(f"Server Error: {response.status_code}")
                        
                except Exception as e:
                    print(f"Connection Error: {e}")
                
                time.sleep(2)  # Realistic user pause
            
            category_score = (category_passed / category_total) * 100
            category_scores[category] = category_score
            print(f"\n{category} Score: {category_passed}/{category_total} ({category_score:.1f}%)")
        
        overall_score = (passed_tests / total_tests) * 100
        
        print(f"\n=== COMPREHENSIVE TEST RESULTS ===")
        for category, score in category_scores.items():
            status = "EXCELLENT" if score >= 90 else "GOOD" if score >= 75 else "NEEDS WORK" if score >= 60 else "POOR"
            print(f"{status}: {category}: {score:.1f}%")
        
        print(f"\n=== OVERALL PERFORMANCE ===")
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Overall Score: {overall_score:.1f}%")
        
        if overall_score >= 90:
            print("EXCEPTIONAL! System exceeds production standards!")
        elif overall_score >= 80:
            print("EXCELLENT! System is production-ready!")
        elif overall_score >= 70:
            print("GOOD! System is working well!")
        else:
            print("NEEDS IMPROVEMENT! System requires optimization!")
        
        return overall_score, category_scores
    
    def evaluate_response_quality(self, user_message, ai_response, expected):
        """Comprehensive response evaluation with detailed scoring"""
        response_lower = ai_response.lower()
        message_lower = user_message.lower()
        
        scores = []
        
        # 1. Techrypt Branding (20 points)
        if 'techrypt' in response_lower:
            scores.append(20)
        else:
            scores.append(0)
        
        # 2. Consultation Offer (20 points)
        consultation_words = ['consultation', 'schedule', 'discuss', 'meeting', 'call']
        if any(word in response_lower for word in consultation_words):
            scores.append(20)
        else:
            scores.append(0)
        
        # 3. Business Context Recognition (25 points)
        business_types = ['cleaning', 'pet', 'restaurant', 'gym', 'shop', 'store', 'business']
        context_score = 0
        for btype in business_types:
            if btype in message_lower and btype in response_lower:
                context_score = 25
                break
        scores.append(context_score)
        
        # 4. Service Relevance (20 points)
        service_keywords = ['website', 'social media', 'branding', 'chatbot', 'automation', 'payment']
        service_score = 0
        for service in service_keywords:
            if service in message_lower or service in response_lower:
                service_score = 20
                break
        scores.append(service_score)
        
        # 5. Response Quality (15 points)
        quality_score = 0
        if len(ai_response) > 30:
            quality_score += 5
        if not any(bad in response_lower for bad in ['error', 'fail', 'sorry']):
            quality_score += 5
        if any(good in response_lower for good in ['help', 'can', 'will', 'offer']):
            quality_score += 5
        scores.append(quality_score)
        
        total_score = sum(scores)
        return total_score
    
    def analyze_failure(self, user_message, ai_response, expected):
        """Analyze why a test failed"""
        response_lower = ai_response.lower()
        
        issues = []
        
        if 'techrypt' not in response_lower:
            issues.append("Missing Techrypt branding")
        
        consultation_words = ['consultation', 'schedule', 'discuss', 'meeting']
        if not any(word in response_lower for word in consultation_words):
            issues.append("Missing consultation offer")
        
        if len(ai_response) < 30:
            issues.append("Response too short")
        
        if issues:
            print(f"Issues found: {', '.join(issues)}")

def main():
    """Main testing function"""
    tester = ComprehensiveTestPipeline()
    
    # Check server connectivity
    try:
        response = requests.get(f"{tester.server_url}/health", timeout=5)
        if response.status_code != 200:
            print(f"Server not responding at {tester.server_url}")
            return
    except:
        print(f"Cannot connect to server at {tester.server_url}")
        return
    
    print("Server connectivity confirmed")
    
    # Run comprehensive testing
    score, category_scores = tester.run_comprehensive_tests()
    
    print(f"\n=== FINAL ASSESSMENT ===")
    if score >= 80:
        print("SYSTEM IS PRODUCTION READY!")
        print("LLM working perfectly with CSV alignment!")
    else:
        print("SYSTEM NEEDS MORE IMPROVEMENTS")
        print("Continue refining the LLM-CSV hybrid")
    
    print(f"\nTesting completed at {datetime.now()}")

if __name__ == "__main__":
    main()
