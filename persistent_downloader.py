#!/usr/bin/env python3
"""
PERSISTENT LLM DOWNLOADER
Automatically resumes downloads if they stop, never gives up
"""

import os
import sys
import time
import traceback
from datetime import datetime

def setup_environment():
    """Setup aggressive download environment"""
    # Clear offline modes
    env_vars_to_clear = ['TRANSFORMERS_OFFLINE', 'HF_HUB_OFFLINE', 'HF_HUB_DISABLE_PROGRESS_BARS']
    for var in env_vars_to_clear:
        if var in os.environ:
            del os.environ[var]
    
    # Set aggressive timeouts and retries
    os.environ['HF_HUB_DOWNLOAD_TIMEOUT'] = '7200'  # 2 hours
    os.environ['HF_HUB_DOWNLOAD_RETRIES'] = '50'    # 50 retries
    os.environ['CURL_CA_BUNDLE'] = ''
    os.environ['REQUESTS_CA_BUNDLE'] = ''

def check_model_status(model_name):
    """Check if model is fully downloaded"""
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        tokenizer = AutoTokenizer.from_pretrained(model_name, local_files_only=True)
        model = AutoModelForCausalLM.from_pretrained(model_name, local_files_only=True)
        return True
    except:
        return False

def download_with_persistence(model_name, max_attempts=1000):
    """Download model with infinite persistence"""
    attempt = 0
    
    while attempt < max_attempts:
        attempt += 1
        current_time = datetime.now().strftime("%H:%M:%S")
        
        print(f"\n[{current_time}] ATTEMPT {attempt}: {model_name}")
        print("=" * 60)
        
        # Check if already complete
        if check_model_status(model_name):
            print(f"SUCCESS: {model_name} is fully downloaded and ready!")
            return True
        
        try:
            from transformers import AutoTokenizer, AutoModelForCausalLM
            import torch
            
            print("Step 1: Downloading/checking tokenizer...")
            tokenizer = AutoTokenizer.from_pretrained(
                model_name,
                resume_download=True,
                force_download=False
            )
            print("Tokenizer ready")
            
            print("Step 2: Downloading/resuming model...")
            model = AutoModelForCausalLM.from_pretrained(
                model_name,
                resume_download=True,
                force_download=False,
                torch_dtype=torch.float32,
                low_cpu_mem_usage=True
            )
            print("Model download completed!")
            
            print("Step 3: Testing model...")
            from transformers import pipeline
            pipe = pipeline('text-generation', model=model, tokenizer=tokenizer)
            result = pipe("Hello", max_length=20)
            print(f"Test successful: {result[0]['generated_text'][:50]}")
            
            print(f"\nSUCCESS: {model_name} is now fully ready!")
            return True
            
        except KeyboardInterrupt:
            print("\nUser interrupted download")
            return False
            
        except Exception as e:
            error_msg = str(e)[:200]
            print(f"Attempt {attempt} failed: {error_msg}")
            
            # Wait before retry (exponential backoff, max 60 seconds)
            wait_time = min(60, attempt * 5)
            print(f"Waiting {wait_time} seconds before retry...")
            time.sleep(wait_time)
            
            # Clear any cached imports to start fresh
            if 'transformers' in sys.modules:
                del sys.modules['transformers']
            
            continue
    
    print(f"FAILED: {model_name} failed after {max_attempts} attempts")
    return False

def monitor_and_resume():
    """Monitor downloads and resume if needed"""
    models_to_download = [
        'microsoft/DialoGPT-medium',
        'microsoft/DialoGPT-small',
        'distilgpt2',
        'gpt2'
    ]
    
    print("PERSISTENT LLM DOWNLOADER STARTED")
    print(f"Time: {datetime.now()}")
    print("=" * 80)
    print("This script will keep running until at least one model is downloaded")
    print("It will automatically resume if downloads are interrupted")
    print("Press Ctrl+C to stop")
    print("=" * 80)
    
    setup_environment()
    
    while True:
        try:
            # Check which models are already complete
            completed_models = []
            pending_models = []
            
            for model in models_to_download:
                if check_model_status(model):
                    completed_models.append(model)
                else:
                    pending_models.append(model)
            
            current_time = datetime.now().strftime("%H:%M:%S")
            print(f"\n[{current_time}] STATUS CHECK:")
            print(f"Completed: {len(completed_models)}")
            print(f"Pending: {len(pending_models)}")
            
            if completed_models:
                print("COMPLETED MODELS:")
                for model in completed_models:
                    print(f"  ✅ {model}")
            
            if not pending_models:
                print("\n🎉 ALL MODELS DOWNLOADED SUCCESSFULLY!")
                print("LLM system is now complete!")
                break
            
            # Download the first pending model
            target_model = pending_models[0]
            print(f"\nTARGET: {target_model}")
            
            success = download_with_persistence(target_model)
            
            if success:
                print(f"✅ {target_model} completed successfully!")
                continue
            else:
                print(f"❌ {target_model} failed, trying next model...")
                # Move failed model to end of list
                models_to_download.remove(target_model)
                models_to_download.append(target_model)
                time.sleep(10)
                continue
                
        except KeyboardInterrupt:
            print("\n\nUser stopped the persistent downloader")
            break
            
        except Exception as e:
            print(f"\nUnexpected error: {e}")
            print("Restarting in 30 seconds...")
            time.sleep(30)
            continue

def main():
    """Main function"""
    try:
        monitor_and_resume()
    except KeyboardInterrupt:
        print("\nPersistent downloader stopped by user")
    except Exception as e:
        print(f"\nFatal error: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
