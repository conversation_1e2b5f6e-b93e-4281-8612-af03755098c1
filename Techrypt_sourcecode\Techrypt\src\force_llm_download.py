#!/usr/bin/env python3
"""
🚀 FORCE LLM DOWNLOAD
Forces DialoGPT-medium download with retry logic and progress tracking
"""

import os
import sys
import time
import subprocess
from datetime import datetime

def force_download_dialogpt_medium():
    """Force download DialoGPT-medium with aggressive retry logic"""
    print("🚀 FORCING DIALOGPT-MEDIUM DOWNLOAD")
    print(f"⏰ Started at: {datetime.now()}")
    print("=" * 60)
    
    # Clear any offline environment variables
    env_vars_to_clear = [
        'TRANSFORMERS_OFFLINE',
        'HF_HUB_OFFLINE',
        'HF_HUB_DISABLE_PROGRESS_BARS'
    ]
    
    for var in env_vars_to_clear:
        if var in os.environ:
            del os.environ[var]
            print(f"✅ Cleared {var}")
    
    # Set aggressive download settings
    os.environ['HF_HUB_DOWNLOAD_TIMEOUT'] = '600'  # 10 minutes
    os.environ['CURL_CA_BUNDLE'] = ''  # Clear SSL bundle issues
    
    print("🌐 Environment configured for aggressive download")
    
    # Download script with retry logic
    download_script = '''
import os
import time
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from huggingface_hub import snapshot_download

def download_with_retry(model_name, max_retries=5):
    """Download with retry logic"""
    for attempt in range(max_retries):
        try:
            print(f"\\n🔄 Attempt {attempt + 1}/{max_retries}: Downloading {model_name}")
            
            # Method 1: Direct transformers download
            print("📥 Downloading tokenizer...")
            tokenizer = AutoTokenizer.from_pretrained(
                model_name,
                resume_download=True,
                force_download=False,
                cache_dir=None
            )
            print("✅ Tokenizer downloaded successfully!")
            
            print("📥 Downloading model...")
            model = AutoModelForCausalLM.from_pretrained(
                model_name,
                resume_download=True,
                force_download=False,
                cache_dir=None,
                torch_dtype=torch.float32,
                low_cpu_mem_usage=True
            )
            print("✅ Model downloaded successfully!")
            
            # Test the model
            print("🧪 Testing model...")
            from transformers import pipeline
            chatbot = pipeline(
                'text-generation',
                model=model,
                tokenizer=tokenizer,
                max_length=50,
                do_sample=True,
                temperature=0.7
            )
            
            test_result = chatbot("Hello, I need help with", max_length=30)
            print(f"✅ Model test successful: {test_result[0]['generated_text']}")
            
            return True
            
        except Exception as e:
            print(f"❌ Attempt {attempt + 1} failed: {e}")
            if attempt < max_retries - 1:
                wait_time = (attempt + 1) * 30  # Exponential backoff
                print(f"⏳ Waiting {wait_time} seconds before retry...")
                time.sleep(wait_time)
            else:
                print("💥 All download attempts failed!")
                return False
    
    return False

# Try downloading DialoGPT-medium
success = download_with_retry('microsoft/DialoGPT-medium')

if success:
    print("\\n🎉 DIALOGPT-MEDIUM DOWNLOAD COMPLETED!")
    print("✅ Model is cached and ready for use")
else:
    print("\\n❌ DOWNLOAD FAILED - Trying smaller model...")
    # Fallback to small model
    success_small = download_with_retry('microsoft/DialoGPT-small')
    if success_small:
        print("✅ DialoGPT-small downloaded as fallback")
    else:
        print("❌ All downloads failed")
'''
    
    try:
        print("🔄 Starting aggressive download process...")
        
        # Run the download script
        result = subprocess.run([
            sys.executable, '-c', download_script
        ], capture_output=True, text=True, timeout=1800)  # 30 minute timeout
        
        print("📊 DOWNLOAD RESULTS:")
        print("=" * 40)
        
        if result.stdout:
            print("STDOUT:")
            print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("\n🎉 DOWNLOAD SUCCESSFUL!")
            return True
        else:
            print(f"\n❌ Download failed with return code: {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Download timed out after 30 minutes")
        return False
    except Exception as e:
        print(f"❌ Download process failed: {e}")
        return False

def verify_model_availability():
    """Verify that the model is available and working"""
    print("\n🔍 VERIFYING MODEL AVAILABILITY")
    print("=" * 40)
    
    verification_script = '''
try:
    from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline
    import torch
    
    models_to_check = [
        'microsoft/DialoGPT-medium',
        'microsoft/DialoGPT-small',
        'distilgpt2',
        'gpt2'
    ]
    
    working_models = []
    
    for model_name in models_to_check:
        try:
            print(f"🔍 Checking {model_name}...")
            
            # Try to load from cache only
            tokenizer = AutoTokenizer.from_pretrained(model_name, local_files_only=True)
            model = AutoModelForCausalLM.from_pretrained(model_name, local_files_only=True)
            
            # Quick test
            pipeline_test = pipeline('text-generation', model=model, tokenizer=tokenizer)
            test_result = pipeline_test("Hello", max_length=20)
            
            print(f"✅ {model_name} - WORKING")
            working_models.append(model_name)
            
        except Exception as e:
            print(f"❌ {model_name} - NOT AVAILABLE: {e}")
    
    print(f"\\n📊 SUMMARY:")
    print(f"Working models: {len(working_models)}")
    for model in working_models:
        print(f"✅ {model}")
    
    if working_models:
        print(f"\\n🎯 BEST MODEL: {working_models[0]}")
        return working_models[0]
    else:
        print("\\n❌ NO MODELS AVAILABLE")
        return None
        
except Exception as e:
    print(f"❌ Verification failed: {e}")
    return None
'''
    
    try:
        result = subprocess.run([
            sys.executable, '-c', verification_script
        ], capture_output=True, text=True, timeout=120)
        
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

def main():
    """Main download process"""
    print("🎯 DIALOGPT-MEDIUM FORCE DOWNLOAD")
    print("=" * 80)
    
    # Step 1: Force download
    download_success = force_download_dialogpt_medium()
    
    # Step 2: Verify availability
    verification_success = verify_model_availability()
    
    # Step 3: Final status
    print(f"\n🎯 FINAL STATUS:")
    print("=" * 40)
    
    if download_success and verification_success:
        print("🎉 SUCCESS! DialoGPT-medium is ready!")
        print("✅ LLM system is now complete")
        print("🚀 Ready to restart AI server with LLM")
    elif verification_success:
        print("✅ Models available (may be smaller version)")
        print("🚀 Ready to restart AI server")
    else:
        print("❌ LLM download failed")
        print("💡 System will use Enhanced CSV mode")
    
    print(f"\n⏰ Completed at: {datetime.now()}")

if __name__ == "__main__":
    main()
