#!/usr/bin/env python3
"""
🔄 RESUME LLM DOWNLOADS
Resumes paused downloads with aggressive retry logic
"""

import os
import sys
import time
import subprocess
from datetime import datetime

def setup_aggressive_download_environment():
    """Setup environment for aggressive downloads"""
    print("🔧 Setting up aggressive download environment...")
    
    # Clear offline modes
    env_vars_to_clear = [
        'TRANSFORMERS_OFFLINE',
        'HF_HUB_OFFLINE',
        'HF_HUB_DISABLE_PROGRESS_BARS'
    ]
    
    for var in env_vars_to_clear:
        if var in os.environ:
            del os.environ[var]
    
    # Set aggressive download settings
    os.environ['HF_HUB_DOWNLOAD_TIMEOUT'] = '1800'  # 30 minutes
    os.environ['HF_HUB_DOWNLOAD_RETRIES'] = '20'    # 20 retries
    os.environ['CURL_CA_BUNDLE'] = ''               # Clear SSL bundle
    os.environ['REQUESTS_CA_BUNDLE'] = ''           # Clear SSL bundle
    os.environ['TRANSFORMERS_CACHE'] = os.path.expanduser('~/.cache/huggingface/transformers')
    
    print("✅ Environment configured for aggressive downloads")

def resume_model_download(model_name, description):
    """Resume download for a specific model"""
    print(f"\n🔄 Resuming download: {description}")
    print(f"Model: {model_name}")
    print("=" * 60)
    
    download_script = f'''
import os
import time
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from huggingface_hub import snapshot_download

def download_with_aggressive_retry(model_name, max_retries=10):
    """Download with aggressive retry logic"""
    for attempt in range(max_retries):
        try:
            print(f"\\n🔄 Attempt {{attempt + 1}}/{{max_retries}}: Resuming {model_name}")
            
            # Method 1: Resume tokenizer download
            print("📥 Resuming tokenizer download...")
            tokenizer = AutoTokenizer.from_pretrained(
                model_name,
                resume_download=True,
                force_download=False,
                cache_dir=None,
                local_files_only=False
            )
            print("✅ Tokenizer completed!")
            
            # Method 2: Resume model download
            print("📥 Resuming model download...")
            model = AutoModelForCausalLM.from_pretrained(
                model_name,
                resume_download=True,
                force_download=False,
                cache_dir=None,
                local_files_only=False,
                torch_dtype=torch.float32,
                low_cpu_mem_usage=True
            )
            print("✅ Model download completed!")
            
            # Test the model
            print("🧪 Testing model...")
            from transformers import pipeline
            chatbot = pipeline(
                'text-generation',
                model=model,
                tokenizer=tokenizer,
                max_length=50,
                do_sample=True,
                temperature=0.7
            )
            
            test_result = chatbot("Hello, I need help with", max_length=30)
            print(f"✅ Model test successful: {{test_result[0]['generated_text']}}")
            
            return True
            
        except Exception as e:
            print(f"❌ Attempt {{attempt + 1}} failed: {{e}}")
            if attempt < max_retries - 1:
                wait_time = min(60, (attempt + 1) * 10)  # Max 60 seconds wait
                print(f"⏳ Waiting {{wait_time}} seconds before retry...")
                time.sleep(wait_time)
            else:
                print("💥 All retry attempts failed!")
                return False
    
    return False

# Resume download
success = download_with_aggressive_retry('{model_name}')

if success:
    print("\\n🎉 {description} DOWNLOAD COMPLETED!")
    print("✅ Model is cached and ready for use")
else:
    print("\\n❌ {description} download failed after all retries")
'''
    
    try:
        print(f"🔄 Starting aggressive resume for {model_name}...")
        
        result = subprocess.run([
            sys.executable, '-c', download_script
        ], capture_output=True, text=True, timeout=2400)  # 40 minute timeout
        
        print("📊 DOWNLOAD RESULTS:")
        print("=" * 40)
        
        if result.stdout:
            print("OUTPUT:")
            print(result.stdout)
        
        if result.stderr:
            print("ERRORS:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"\n🎉 {description} DOWNLOAD SUCCESSFUL!")
            return True
        else:
            print(f"\n❌ {description} download failed with return code: {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} download timed out after 40 minutes")
        return False
    except Exception as e:
        print(f"❌ {description} download process failed: {e}")
        return False

def check_download_progress():
    """Check current download progress"""
    print("🔍 CHECKING CURRENT DOWNLOAD PROGRESS")
    print("=" * 50)
    
    check_script = '''
try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
    import os
    
    models_to_check = [
        ('microsoft/DialoGPT-medium', 'DialoGPT-medium (Primary)'),
        ('microsoft/DialoGPT-small', 'DialoGPT-small (Fallback)'),
        ('distilgpt2', 'DistilGPT2 (Lightweight)'),
        ('gpt2', 'GPT2 (Final fallback)')
    ]
    
    working_models = []
    
    for model_name, description in models_to_check:
        try:
            print(f"🔍 Checking {description}...")
            
            # Try to load from cache only first
            try:
                tokenizer = AutoTokenizer.from_pretrained(model_name, local_files_only=True)
                model = AutoModelForCausalLM.from_pretrained(model_name, local_files_only=True)
                print(f"✅ {description} - FULLY CACHED")
                working_models.append((model_name, description, 'cached'))
                continue
            except:
                pass
            
            # Check if partially downloaded
            cache_dir = os.path.expanduser('~/.cache/huggingface/transformers')
            model_cache_path = os.path.join(cache_dir, f"models--{model_name.replace('/', '--')}")
            
            if os.path.exists(model_cache_path):
                print(f"🔄 {description} - PARTIALLY DOWNLOADED")
                working_models.append((model_name, description, 'partial'))
            else:
                print(f"❌ {description} - NOT STARTED")
                
        except Exception as e:
            print(f"❌ {description} - ERROR: {e}")
    
    print(f"\\n📊 SUMMARY:")
    print(f"Models found: {len(working_models)}")
    for model_name, description, status in working_models:
        status_icon = "✅" if status == 'cached' else "🔄" if status == 'partial' else "❌"
        print(f"{status_icon} {description} - {status.upper()}")
    
    return working_models
        
except Exception as e:
    print(f"❌ Progress check failed: {e}")
    return []
'''
    
    try:
        result = subprocess.run([
            sys.executable, '-c', check_script
        ], capture_output=True, text=True, timeout=60)
        
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Progress check failed: {e}")
        return False

def main():
    """Main resume download process"""
    print("🔄 RESUME LLM DOWNLOADS")
    print(f"⏰ Started at: {datetime.now()}")
    print("=" * 80)
    
    # Step 1: Setup environment
    setup_aggressive_download_environment()
    
    # Step 2: Check current progress
    check_download_progress()
    
    # Step 3: Resume downloads in order of priority
    models_to_resume = [
        ('microsoft/DialoGPT-medium', 'DialoGPT-medium (Primary - 37% complete)'),
        ('microsoft/DialoGPT-small', 'DialoGPT-small (Fallback - 7% complete)'),
        ('distilgpt2', 'DistilGPT2 (Lightweight - 15% complete)'),
        ('gpt2', 'GPT2 (Final fallback - 4% complete)')
    ]
    
    successful_downloads = 0
    
    for model_name, description in models_to_resume:
        print(f"\n{'='*60}")
        success = resume_model_download(model_name, description)
        if success:
            successful_downloads += 1
            print(f"🎉 {description} completed successfully!")
            break  # Stop after first successful download
        else:
            print(f"💥 {description} failed - trying next model...")
    
    # Step 4: Final status
    print(f"\n🎯 FINAL DOWNLOAD STATUS:")
    print("=" * 50)
    
    if successful_downloads > 0:
        print("🎉 SUCCESS! At least one LLM model is ready!")
        print("✅ LLM system can now be activated")
        print("🚀 Ready to restart AI server with LLM")
    else:
        print("❌ All LLM downloads failed")
        print("💡 System will continue using Enhanced CSV mode")
        print("🔧 May need manual intervention or different approach")
    
    print(f"\n⏰ Completed at: {datetime.now()}")

if __name__ == "__main__":
    main()
