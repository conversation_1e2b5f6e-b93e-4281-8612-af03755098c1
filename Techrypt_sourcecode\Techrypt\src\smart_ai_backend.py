#!/usr/bin/env python3
"""
Smart AI Backend for Techrypt Chatbot
Clean, intelligent LLM-based system with minimal hardcoding
"""

import os
import csv
import logging
from datetime import datetime
from typing import Dict, List, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Try to import Hugging Face transformers
try:
    from transformers import pipeline
    HF_AVAILABLE = True
    logger.info("Hugging Face Transformers available")
except ImportError:
    HF_AVAILABLE = False
    logger.warning("Hugging Face Transformers not available")

class SmartTechryptAI:
    """Clean, intelligent AI system for Techrypt"""
    
    def __init__(self):
        """Initialize the smart AI system"""
        self.conversation_history = []
        self.user_context = {
            'name': '',
            'conversation_count': 0
        }
        
        # Load CSV training data
        self.training_data = self.load_csv_data()
        
        # Initialize intelligent LLM
        self.llm_pipeline = self.initialize_smart_llm()
        
        logger.info("Smart Techrypt AI initialized successfully")

    def load_csv_data(self):
        """Load training data from CSV file"""
        training_data = []
        csv_path = os.path.join(os.path.dirname(__file__), 'data.csv')
        
        try:
            with open(csv_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    training_data.append({
                        'question': row['question'].lower(),
                        'answer': row['answer'],
                        'category': row['category'],
                        'keywords': row['keywords'].lower().split()
                    })
            logger.info(f"Loaded {len(training_data)} training examples from CSV")
        except FileNotFoundError:
            logger.warning("CSV training data file not found")
        except Exception as e:
            logger.error(f"Error loading CSV data: {e}")
        
        return training_data

    def initialize_smart_llm(self):
        """Initialize intelligent LLM for smart responses"""
        if not HF_AVAILABLE:
            logger.warning("LLM not available, using CSV fallback")
            return None
            
        try:
            # Use a lightweight but intelligent model
            model_name = "microsoft/DialoGPT-small"
            logger.info(f"Loading smart LLM model: {model_name}")
            
            chatbot = pipeline(
                "text-generation",
                model=model_name,
                tokenizer=model_name,
                device=-1,  # Use CPU
                max_length=200,
                do_sample=True,
                temperature=0.8,
                pad_token_id=50256
            )
            
            logger.info("Smart LLM model loaded successfully")
            return chatbot
            
        except Exception as e:
            logger.error(f"Failed to load LLM model: {e}")
            return None

    def generate_smart_response(self, user_message: str, user_name: str = '') -> str:
        """Generate intelligent response using LLM with business context"""
        
        # Update context
        if user_name and user_name.strip():
            self.user_context['name'] = user_name.strip()
        elif self.user_context['conversation_count'] == 0:
            self.user_context['name'] = ''
            
        self.user_context['conversation_count'] += 1
        
        # Try intelligent LLM response first
        if self.llm_pipeline:
            try:
                smart_response = self.get_intelligent_llm_response(user_message)
                if smart_response and len(smart_response.strip()) > 20:
                    logger.info("Using intelligent LLM response")
                    return smart_response
            except Exception as e:
                logger.error(f"LLM response failed: {e}")
        
        # Fallback to CSV-based response
        csv_response = self.get_csv_response(user_message)
        if csv_response:
            return csv_response
            
        # Final fallback
        return self.get_default_response()

    def get_intelligent_llm_response(self, user_message: str) -> str:
        """Get intelligent response from LLM with business context"""
        
        # Create intelligent context prompt
        context = f"""You are an intelligent AI assistant for Techrypt.io, a digital marketing agency.

COMPANY SERVICES:
1. Website Development - Custom websites, e-commerce stores, SEO optimization
2. Social Media Marketing - Instagram, Facebook, LinkedIn, TikTok growth
3. Branding Services - Logo design, brand identity, marketing materials
4. Chatbot Development - AI-powered customer service automation
5. Automation Packages - Business process automation, workflow optimization
6. Payment Gateway Integration - Stripe, PayPal, secure payment systems

INTELLIGENCE INSTRUCTIONS:
- Analyze user input to detect business type and service needs
- Recognize business platforms (Daraz=ecommerce, Shopify=ecommerce, etc.)
- Provide structured responses with numbered services when relevant
- Be conversational but professional
- Always offer consultation booking
- Use user's name only if provided, otherwise don't use any name
- Keep responses concise and helpful

RESPONSE FORMAT:
- For business inquiries: "Great! For your [business type], we can help with: [numbered list of relevant services]"
- For service inquiries: Focus on the specific service requested
- For greetings: Welcome and ask how you can help
- Always end with consultation offer"""

        # Add user context
        if self.user_context['name']:
            context += f"\nUser's name: {self.user_context['name']}"
            
        if self.conversation_history:
            recent = " ".join([conv['user'] for conv in self.conversation_history[-2:]])
            context += f"\nRecent conversation: {recent}"

        # Generate response
        full_prompt = f"{context}\n\nUser: {user_message}\nAssistant:"
        
        try:
            response = self.llm_pipeline(
                full_prompt,
                max_new_tokens=150,
                do_sample=True,
                temperature=0.8,
                pad_token_id=50256,
                truncation=True
            )
            
            if response and len(response) > 0:
                generated_text = response[0]['generated_text']
                
                # Extract assistant response
                if "Assistant:" in generated_text:
                    assistant_response = generated_text.split("Assistant:")[-1].strip()
                else:
                    assistant_response = generated_text.replace(full_prompt, "").strip()
                
                # Clean up response
                assistant_response = assistant_response.replace("User:", "").strip()
                assistant_response = assistant_response.replace("*", "")  # Remove asterisks
                
                return assistant_response[:400] if assistant_response else None
                
        except Exception as e:
            logger.error(f"LLM generation error: {e}")
            
        return None

    def get_csv_response(self, user_message: str) -> str:
        """Get response from CSV training data"""
        if not self.training_data:
            return None
            
        msg_lower = user_message.lower()
        best_match = None
        best_score = 0
        
        for data in self.training_data:
            # Check for keyword matches
            keyword_matches = sum(1 for keyword in data['keywords'] if keyword in msg_lower)
            
            if keyword_matches > 0:
                # Simple similarity score
                similarity = len(set(msg_lower.split()) & set(data['question'].split())) / len(set(data['question'].split()))
                score = (keyword_matches * 0.6) + (similarity * 0.4)
                
                if score > best_score and score > 0.3:
                    best_score = score
                    best_match = data
        
        if best_match:
            response = best_match['answer']
            # Personalize if name available
            if self.user_context['name'] and self.user_context['name'] not in response:
                if response.startswith('Great!'):
                    response = f"Great, {self.user_context['name']}!" + response[6:]
                elif response.startswith('Hello!'):
                    response = f"Hello, {self.user_context['name']}!" + response[6:]
            
            # Remove asterisks
            response = response.replace('*', '')
            return response
            
        return None

    def get_default_response(self) -> str:
        """Get default response when no match found"""
        name_part = f", {self.user_context['name']}" if self.user_context['name'] else ""
        
        return f"""Thank you for your message{name_part}! I'm here to help you with Techrypt.io's digital services:

1. Website Development - Custom websites with SEO optimization
2. Social Media Marketing - Instagram, Facebook, LinkedIn growth  
3. Branding Services - Logo design, brand identity, marketing materials
4. Chatbot Development - AI-powered customer service automation
5. Automation Packages - Business process automation solutions
6. Payment Gateway Integration - Secure payment system integration

Could you tell me more about what you're looking for, or would you like to schedule a consultation?"""

    def add_to_history(self, user_message: str, bot_response: str):
        """Add conversation to history"""
        self.conversation_history.append({
            'timestamp': datetime.now().isoformat(),
            'user': user_message,
            'bot': bot_response
        })
        
        # Keep only last 10 exchanges
        if len(self.conversation_history) > 10:
            self.conversation_history = self.conversation_history[-10:]

    def reset_context(self):
        """Reset conversation context"""
        self.conversation_history = []
        self.user_context = {
            'name': '',
            'conversation_count': 0
        }
        logger.info("AI context reset for new session")

# Global AI instance
smart_ai = SmartTechryptAI()

def get_smart_ai_response(user_message: str, user_name: str = '') -> str:
    """Main function to get smart AI response"""
    try:
        response = smart_ai.generate_smart_response(user_message, user_name)
        smart_ai.add_to_history(user_message, response)
        return response
    except Exception as e:
        logger.error(f"Error generating smart AI response: {e}")
        return "I apologize, but I'm having trouble processing your request. Please try again or contact our support team."

def reset_smart_ai():
    """Reset the smart AI context"""
    smart_ai.reset_context()

if __name__ == "__main__":
    # Test the smart AI
    print("Testing Smart Techrypt AI...")
    
    test_messages = [
        "hello",
        "daraz",
        "I have a restaurant",
        "I need social media marketing",
        "what services do you offer"
    ]
    
    for msg in test_messages:
        response = get_smart_ai_response(msg, "")
        print(f"\nUser: {msg}")
        print(f"AI: {response}")
