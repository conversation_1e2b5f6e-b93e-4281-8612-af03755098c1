# Techrypt Integrated Chatbot System

## 🚀 Overview

This is the complete integration of the AI-powered chatbot into the original Techrypt.io website. The chatbot replaces the WhatsApp icon in the sidebar and provides intelligent customer service with appointment scheduling capabilities.

## ✨ Features

### 🤖 AI-Powered Chatbot
- **Smart LLM Integration**: Trained on 10,000+ CSV data entries
- **Natural Language Processing**: Understands complex business queries
- **Voice Activation**: Click-to-activate voice input and text-to-speech
- **Intelligent Responses**: Context-aware conversations

### 📅 Appointment Scheduling
- **Real-time Booking**: Direct integration with backend database
- **Conflict Detection**: Prevents double-booking
- **Service Selection**: Multiple service categories available
- **Email Confirmations**: Automatic confirmation emails

### 📱 Contact Management
- **First-Message Contact Form**: Automatically collects user information
- **Pre-filled Forms**: Saves user data for future interactions
- **Phone Validation**: Numeric-only phone number input
- **Data Persistence**: Contact information saved to database

### 🎨 Design Integration
- **Techrypt.io Styling**: Matches original website design perfectly
- **Responsive Design**: Works on all device sizes
- **Modern UI**: Gradient backgrounds and smooth animations
- **Accessibility**: Full keyboard and screen reader support

## 🏗️ Architecture

```
Techrypt Integrated System
├── Frontend (React + Vite)
│   ├── Original Techrypt.io Website
│   └── Integrated TechryptChatbot Component
├── Backend Services
│   ├── Express.js API Server (Port 5000)
│   ├── AI Backend (Flask - Port 5001)
│   └── MongoDB Database
└── AI Model
    ├── CSV Training Data (10,000+ entries)
    ├── Smart LLM Integration
    └── Context-Aware Responses
```

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- Python (v3.8 or higher)
- MongoDB
- Git

### Installation & Setup

1. **Start All Services** (Recommended):
   ```bash
   # Windows Batch
   start-techrypt-integrated.bat
   
   # Or PowerShell
   .\start-techrypt-integrated.ps1
   ```

2. **Manual Setup**:
   ```bash
   # 1. Start MongoDB
   mongod --dbpath=C:\data\db
   
   # 2. Start Backend Server
   cd server
   npm install
   npm start
   
   # 3. Start AI Backend
   cd ai_backend
   pip install -r requirements.txt
   python smart_llm_chatbot.py
   
   # 4. Start Techrypt Frontend
   cd Techrypt_sourcecode/Techrypt
   npm install
   npm run dev
   ```

### Access Points
- **Techrypt Website**: http://localhost:5173
- **Backend API**: http://localhost:5000
- **AI Backend**: http://localhost:5001
- **MongoDB**: localhost:27017

## 🎯 Usage

### Opening the Chatbot
1. Visit the Techrypt website at http://localhost:5173
2. Look for the **AI robot icon** in the bottom-right corner
3. Click the icon to open the chatbot modal

### First Interaction
1. **Contact Form**: Automatically appears on first message
2. **Fill Information**: Name, email, and optional phone number
3. **Save & Continue**: Information is saved for future interactions

### Scheduling Appointments
1. **Request Service**: Ask about services or say "schedule appointment"
2. **Service Selection**: Choose from available service categories
3. **Date & Time**: Select preferred appointment slot
4. **Confirmation**: Receive instant confirmation with booking ID

### Voice Features
1. **Voice Input**: Click the microphone icon to speak
2. **Text-to-Speech**: Click the speaker icon on bot messages
3. **Hands-free**: Perfect for accessibility and convenience

## 🔧 Configuration

### Environment Variables (.env)
```env
# Original Techrypt Configuration
VITE_EMAILJS_SERVICE_ID=service_kdyok6m
VITE_EMAILJS_TEMPLATE_ID=template_89ocd8n
VITE_EMAILJS_PUBLIC_KEY=AGyxun3QOu_LV9L5m

# Chatbot Integration
REACT_APP_API_URL=http://localhost:5001
REACT_APP_BACKEND_URL=http://localhost:5000
REACT_APP_CHATBOT_ENABLED=true
```

### Customization Options
- **AI Responses**: Modify `ai_backend/data.csv` for custom responses
- **Services**: Update service list in appointment form
- **Styling**: Customize colors in `TechryptChatbot.css`
- **Business Hours**: Modify in appointment form component

## 📊 Database Schema

### Appointments Collection
```javascript
{
  _id: ObjectId,
  name: String,
  email: String,
  phone: String,
  service: String,
  dateTime: Date,
  notes: String,
  duration: Number,
  source: String,
  status: String,
  createdAt: Date
}
```

### Contacts Collection
```javascript
{
  _id: ObjectId,
  name: String,
  email: String,
  phone: String,
  sessionId: String,
  createdAt: Date,
  lastInteraction: Date
}
```

## 🛠️ Development

### File Structure
```
Techrypt_sourcecode/Techrypt/
├── src/
│   ├── components/
│   │   ├── MessageSidebar/
│   │   │   ├── MessageSidebar.jsx (Modified)
│   │   │   └── MessageSidebar.css
│   │   └── TechryptChatbot/ (New)
│   │       ├── TechryptChatbot.jsx
│   │       └── TechryptChatbot.css
│   └── ...
├── package.json (Updated with axios)
└── .env (Updated with chatbot config)
```

### Key Components
- **MessageSidebar**: Modified to show AI icon and handle chatbot toggle
- **TechryptChatbot**: Main chatbot component with full functionality
- **Form Modals**: Contact and appointment forms integrated

## 🔍 Troubleshooting

### Common Issues

1. **Chatbot Not Opening**
   - Check if all services are running
   - Verify axios is installed: `npm list axios`
   - Check browser console for errors

2. **AI Responses Not Working**
   - Ensure AI backend is running on port 5001
   - Check `smart_llm_chatbot.py` is running
   - Verify CSV data file exists

3. **Appointment Booking Fails**
   - Check MongoDB connection
   - Verify backend server on port 5000
   - Check appointment API endpoints

4. **Styling Issues**
   - Clear browser cache
   - Check CSS file imports
   - Verify Tailwind CSS is working

### Debug Mode
Enable debug logging by setting:
```javascript
console.log('Debug mode enabled');
```

## 🚀 Deployment

### Production Checklist
- [ ] Update API URLs for production
- [ ] Configure MongoDB for production
- [ ] Set up SSL certificates
- [ ] Configure CORS properly
- [ ] Test all chatbot features
- [ ] Verify appointment booking
- [ ] Test voice features
- [ ] Check mobile responsiveness

## 📞 Support

For technical support or questions:
- **Email**: <EMAIL>
- **Documentation**: This README file
- **Issues**: Check console logs and error messages

## 🎉 Success!

Your Techrypt website now has a fully integrated, AI-powered chatbot that:
- ✅ Replaces the WhatsApp icon with an AI assistant
- ✅ Automatically collects contact information
- ✅ Provides intelligent responses from CSV training data
- ✅ Schedules appointments with conflict detection
- ✅ Maintains the original Techrypt.io design and functionality
- ✅ Supports voice input and text-to-speech
- ✅ Works seamlessly across all devices

**The integration is complete and ready for use!** 🚀
