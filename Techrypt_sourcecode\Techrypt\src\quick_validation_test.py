#!/usr/bin/env python3
"""
🧪 QUICK VALIDATION TEST
Tests a small subset to validate the exhaustive testing system works correctly
"""

import requests
import time
from ultimate_exhaustive_testing import UltimateExhaustiveTester

def quick_validation():
    """Run a quick validation test with a small subset"""
    print("🧪 QUICK VALIDATION TEST")
    print("Testing a small subset to validate the system")
    print("=" * 60)
    
    tester = UltimateExhaustiveTester()
    
    # Check server connectivity
    try:
        response = requests.get(f"{tester.server_url}/health", timeout=5)
        if response.status_code != 200:
            print(f"❌ Server not responding at {tester.server_url}")
            return False
    except:
        print(f"❌ Cannot connect to server at {tester.server_url}")
        return False
    
    print("✅ Server connectivity confirmed")
    
    # Create a small test set
    quick_tests = [
        {
            'category': 'greeting',
            'message': 'hello',
            'user_name': 'alex',
            'expected': {
                'should_contain': ['welcome', 'techrypt', 'help'],
                'should_not_contain': ['error', 'sorry'],
                'response_type': 'hardcoded',
                'quality_threshold': 70
            }
        },
        {
            'category': 'business_detection',
            'message': 'i have a bread business',
            'user_name': 'sarah',
            'business_type': 'bread',
            'expected': {
                'should_contain': ['bread', 'help', 'services'],
                'should_not_contain': ['error', 'cannot assist'],
                'response_type': 'llm_or_business_intelligence',
                'quality_threshold': 80
            }
        },
        {
            'category': 'service_request',
            'message': 'i need a website',
            'user_name': 'mike',
            'service_type': 'website',
            'expected': {
                'should_contain': ['website', 'help', 'consultation'],
                'should_not_contain': ['error', 'don\'t understand'],
                'response_type': 'llm_primary',
                'quality_threshold': 85
            }
        },
        {
            'category': 'garbled_text',
            'message': 'i want a website with chatbot, i will need ypur branding services',
            'user_name': 'alex',
            'expected': {
                'should_not_contain': ['ypur', 'chatbpt', 'shpify', 'busines', 'websiet'],
                'should_contain': ['help', 'techrypt'],
                'response_type': 'llm_with_cleaning',
                'quality_threshold': 75
            }
        },
        {
            'category': 'multiple_services',
            'message': 'i need website, social media, and branding',
            'user_name': 'emma',
            'services': ['website', 'social media', 'branding'],
            'expected': {
                'should_contain': ['website', 'social', 'branding', 'combination'],
                'should_not_contain': ['error', 'garbled'],
                'response_type': 'llm_primary',
                'quality_threshold': 90
            }
        }
    ]
    
    print(f"\n🔍 Running {len(quick_tests)} validation tests...")
    
    # Run the tests
    result = tester.run_test_batch(quick_tests, "Quick Validation")
    
    # Analyze results
    print(f"\n📊 VALIDATION RESULTS:")
    print(f"✅ Passed: {result['passed']}/{result['total_tests']}")
    print(f"❌ Failed: {result['failed']}/{result['total_tests']}")
    print(f"🚨 Errors: {result['errors']}/{result['total_tests']}")
    print(f"📈 Average Score: {result['average_score']:.1f}/100")
    print(f"⏱️ Average Response Time: {result['average_response_time']:.2f}s")
    
    # Check if system is working
    if result['errors'] == 0 and result['passed'] >= 3:
        print("\n✅ VALIDATION PASSED - System is working correctly!")
        print("🚀 Ready to run full exhaustive testing")
        return True
    else:
        print("\n❌ VALIDATION FAILED - Issues detected:")
        for test_detail in result['test_details']:
            if not test_detail['evaluation']['passed']:
                print(f"• {test_detail['category']}: {test_detail['evaluation']['issues'][:2]}")
        return False

if __name__ == "__main__":
    success = quick_validation()
    if success:
        print("\n🎯 To run full exhaustive testing, execute:")
        print("python ultimate_exhaustive_testing.py")
    else:
        print("\n🔧 Fix the issues above before running full testing")
