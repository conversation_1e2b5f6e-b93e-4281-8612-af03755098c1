#!/usr/bin/env python3
"""
COMPREHENSIVE LIVE READINESS TEST
Test if the system is ready for non-technical users in production
"""

import requests
import json
import time

def comprehensive_live_test():
    """Test all critical functionality for live deployment"""
    
    base_url = "http://localhost:5000"
    
    print("🚀 COMPREHENSIVE LIVE READINESS TEST")
    print("=" * 60)
    print("Testing if system is ready for non-technical users")
    print("=" * 60)
    
    # Test categories
    test_results = {
        'basic_functionality': [],
        'user_experience': [],
        'business_scenarios': [],
        'error_handling': [],
        'performance': []
    }
    
    # 1. BASIC FUNCTIONALITY TESTS
    print("\n📋 1. BASIC FUNCTIONALITY TESTS")
    print("-" * 40)
    
    basic_tests = [
        {"input": "hello", "expected": "greeting", "description": "Basic greeting"},
        {"input": "services", "expected": "service_list", "description": "Service list request"},
        {"input": "web scraping", "expected": "web_scraping_service", "description": "Specific service request"},
        {"input": "copywriting", "expected": "copywriting_service", "description": "Copywriting service"},
        {"input": "I have a restaurant", "expected": "restaurant_services", "description": "Business type detection"},
    ]
    
    for test in basic_tests:
        result = run_test(base_url, test['input'], test['expected'], test['description'])
        test_results['basic_functionality'].append(result)
        print(f"  {result['status']} {test['description']}")
    
    # 2. USER EXPERIENCE TESTS
    print("\n👤 2. USER EXPERIENCE TESTS")
    print("-" * 40)
    
    ux_tests = [
        {"input": "", "expected": "error_handling", "description": "Empty message handling"},
        {"input": "hi", "expected": "greeting", "description": "Casual greeting"},
        {"input": "what do you do", "expected": "business_explanation", "description": "General inquiry"},
        {"input": "how much does it cost", "expected": "consultation_offer", "description": "Pricing inquiry"},
        {"input": "can you help me", "expected": "helpful_response", "description": "Help request"},
    ]
    
    for test in ux_tests:
        result = run_test(base_url, test['input'], test['expected'], test['description'])
        test_results['user_experience'].append(result)
        print(f"  {result['status']} {test['description']}")
    
    # 3. BUSINESS SCENARIOS TESTS
    print("\n💼 3. BUSINESS SCENARIOS TESTS")
    print("-" * 40)
    
    business_tests = [
        {"input": "shopify", "expected": "ecommerce_services", "description": "E-commerce platform"},
        {"input": "instagram marketing", "expected": "social_media_service", "description": "Social media service"},
        {"input": "logo design", "expected": "branding_service", "description": "Design service"},
        {"input": "payment gateway", "expected": "payment_service", "description": "Payment service"},
        {"input": "automation", "expected": "automation_service", "description": "Automation service"},
    ]
    
    for test in business_tests:
        result = run_test(base_url, test['input'], test['expected'], test['description'])
        test_results['business_scenarios'].append(result)
        print(f"  {result['status']} {test['description']}")
    
    # 4. ERROR HANDLING TESTS
    print("\n⚠️ 4. ERROR HANDLING TESTS")
    print("-" * 40)
    
    error_tests = [
        {"input": "kidnapping business", "expected": "illegal_rejection", "description": "Illegal activity rejection"},
        {"input": "tell me a joke", "expected": "business_redirect", "description": "Non-business redirect"},
        {"input": "weather today", "expected": "business_redirect", "description": "Off-topic redirect"},
        {"input": "random gibberish xyz", "expected": "fallback_response", "description": "Gibberish handling"},
    ]
    
    for test in error_tests:
        result = run_test(base_url, test['input'], test['expected'], test['description'])
        test_results['error_handling'].append(result)
        print(f"  {result['status']} {test['description']}")
    
    # 5. PERFORMANCE TESTS
    print("\n⚡ 5. PERFORMANCE TESTS")
    print("-" * 40)
    
    performance_tests = [
        {"input": "hello", "max_time": 3.0, "description": "Response time < 3s"},
        {"input": "services", "max_time": 2.0, "description": "Service list < 2s"},
        {"input": "I have a restaurant", "max_time": 5.0, "description": "Complex query < 5s"},
    ]
    
    for test in performance_tests:
        result = run_performance_test(base_url, test['input'], test['max_time'], test['description'])
        test_results['performance'].append(result)
        print(f"  {result['status']} {test['description']}")
    
    # GENERATE FINAL REPORT
    print("\n" + "=" * 60)
    print("🎯 LIVE READINESS REPORT")
    print("=" * 60)
    
    total_tests = sum(len(tests) for tests in test_results.values())
    passed_tests = sum(len([t for t in tests if t['passed']]) for tests in test_results.values())
    
    print(f"📊 OVERALL SCORE: {passed_tests}/{total_tests} ({(passed_tests/total_tests)*100:.1f}%)")
    
    for category, tests in test_results.items():
        category_passed = len([t for t in tests if t['passed']])
        category_total = len(tests)
        category_score = (category_passed/category_total)*100 if category_total > 0 else 0
        
        status_icon = "✅" if category_score >= 80 else "⚠️" if category_score >= 60 else "❌"
        print(f"{status_icon} {category.replace('_', ' ').title()}: {category_passed}/{category_total} ({category_score:.1f}%)")
    
    # FINAL RECOMMENDATION
    overall_score = (passed_tests/total_tests)*100
    
    print("\n🎯 LIVE DEPLOYMENT RECOMMENDATION:")
    if overall_score >= 90:
        print("✅ READY FOR LIVE DEPLOYMENT")
        print("   System performs excellently across all test categories")
    elif overall_score >= 80:
        print("⚠️ READY WITH MINOR ISSUES")
        print("   System is functional but has some areas for improvement")
    elif overall_score >= 70:
        print("⚠️ NEEDS IMPROVEMENTS BEFORE LIVE")
        print("   System has significant issues that should be addressed")
    else:
        print("❌ NOT READY FOR LIVE DEPLOYMENT")
        print("   System has critical issues that must be fixed")
    
    return overall_score

def run_test(base_url, user_input, expected_type, description):
    """Run a single test and return result"""
    try:
        start_time = time.time()
        payload = {"message": user_input, "name": ""}
        response = requests.post(f"{base_url}/chat", json=payload, timeout=10)
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            ai_response = result.get('response', '')
            
            # Analyze response quality
            passed = analyze_response_quality(ai_response, expected_type, user_input)
            
            return {
                'passed': passed,
                'status': "✅" if passed else "❌",
                'response_time': response_time,
                'response': ai_response[:100] + "..." if len(ai_response) > 100 else ai_response
            }
        else:
            return {
                'passed': False,
                'status': "❌",
                'response_time': response_time,
                'response': f"HTTP {response.status_code}"
            }
    except Exception as e:
        return {
            'passed': False,
            'status': "❌",
            'response_time': 0,
            'response': f"Error: {e}"
        }

def run_performance_test(base_url, user_input, max_time, description):
    """Run performance test"""
    try:
        start_time = time.time()
        payload = {"message": user_input, "name": ""}
        response = requests.post(f"{base_url}/chat", json=payload, timeout=max_time + 2)
        response_time = time.time() - start_time
        
        passed = response.status_code == 200 and response_time <= max_time
        
        return {
            'passed': passed,
            'status': "✅" if passed else "❌",
            'response_time': response_time,
            'response': f"{response_time:.2f}s (max: {max_time}s)"
        }
    except Exception as e:
        return {
            'passed': False,
            'status': "❌",
            'response_time': 0,
            'response': f"Error: {e}"
        }

def analyze_response_quality(response, expected_type, user_input):
    """Analyze if response meets quality standards"""
    response_lower = response.lower()
    
    # Basic quality checks
    if len(response.strip()) < 10:
        return False
    
    # Type-specific checks
    if expected_type == "greeting":
        return "hello" in response_lower or "welcome" in response_lower
    elif expected_type == "service_list":
        return "1." in response and "website development" in response_lower
    elif expected_type == "web_scraping_service":
        return "web scraping" in response_lower or "data extraction" in response_lower
    elif expected_type == "copywriting_service":
        return "copywriting" in response_lower or "content creation" in response_lower
    elif expected_type == "restaurant_services":
        return "restaurant" in response_lower and ("website" in response_lower or "social" in response_lower)
    elif expected_type == "illegal_rejection":
        return "cannot assist" in response_lower or "illegal" in response_lower
    elif expected_type == "business_redirect":
        return "business" in response_lower and "help" in response_lower
    elif expected_type == "consultation_offer":
        return "consultation" in response_lower or "schedule" in response_lower
    else:
        # General business relevance check
        business_terms = ['techrypt', 'service', 'business', 'help', 'website', 'social', 'marketing']
        return any(term in response_lower for term in business_terms)

if __name__ == "__main__":
    score = comprehensive_live_test()
    print(f"\n🏁 Final Score: {score:.1f}%")
