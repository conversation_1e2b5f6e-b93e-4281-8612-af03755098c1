#!/usr/bin/env python3
"""
COMPREHENSIVE CHATBOT TESTING SYSTEM
Tests 10,000+ scenarios to achieve 97%+ performance
"""

import requests
import json
import time
import random
from typing import List, Dict, Any
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

class ComprehensiveChatbotTester:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.chat_endpoint = f"{base_url}/chat"
        self.test_results = []
        self.performance_metrics = {
            'total_tests': 0,
            'successful_responses': 0,
            'intelligent_responses': 0,
            'business_detection': 0,
            'service_detection': 0,
            'appointment_triggers': 0,
            'response_time_total': 0,
            'errors': 0
        }
        
    def run_comprehensive_test_suite(self):
        """Run all 10,000+ test scenarios"""
        print("🚀 STARTING COMPREHENSIVE CHATBOT TESTING")
        print("Target: 10,000+ tests with 97%+ performance")
        
        # Test categories
        test_categories = [
            self.test_business_intelligence,
            self.test_service_detection,
            self.test_appointment_scenarios,
            self.test_conversation_flow,
            self.test_edge_cases,
            self.test_performance_stress,
            self.test_real_world_scenarios,
            self.test_error_handling
        ]
        
        for category_func in test_categories:
            print(f"\n📊 Running {category_func.__name__}...")
            category_func()
            
        self.generate_performance_report()
        
    def test_business_intelligence(self):
        """Test business type detection - 2000 tests"""
        business_scenarios = [
            # Electronics business variations
            ("I have an electronics showroom", "electronics"),
            ("I run an electronics store", "electronics"),
            ("My electronics business needs help", "electronics"),
            ("I sell electronics and gadgets", "electronics"),
            ("Electronics showroom marketing needed", "electronics"),
            
            # Restaurant variations
            ("I own a restaurant", "restaurant"),
            ("My cafe needs marketing", "restaurant"),
            ("Food business promotion", "restaurant"),
            ("Restaurant social media help", "restaurant"),
            
            # Retail variations
            ("I have a clothing store", "retail"),
            ("My boutique needs website", "retail"),
            ("Retail shop marketing", "retail"),
            
            # Service business variations
            ("I run a consulting firm", "service"),
            ("My agency needs branding", "service"),
            ("Professional services marketing", "service"),
            
            # Healthcare variations
            ("I have a dental clinic", "healthcare"),
            ("Medical practice website", "healthcare"),
            ("Healthcare marketing services", "healthcare"),
        ]
        
        # Generate 2000 variations
        for i in range(100):
            for scenario, expected_type in business_scenarios:
                variations = [
                    scenario,
                    f"Hi, {scenario.lower()}",
                    f"Hello! {scenario}",
                    f"Good morning, {scenario.lower()}",
                    f"Can you help? {scenario}",
                ]
                
                for variation in variations:
                    self.test_single_scenario(variation, "business_detection", expected_type)
                    
    def test_service_detection(self):
        """Test service detection - 2000 tests"""
        service_scenarios = [
            # Marketing services
            ("I need marketing services", "social_media"),
            ("Social media marketing help", "social_media"),
            ("Instagram marketing needed", "social_media"),
            ("Facebook promotion required", "social_media"),
            
            # Website services
            ("I need a website", "website"),
            ("Website development required", "website"),
            ("Online presence needed", "website"),
            ("Web design services", "website"),
            
            # Branding services
            ("Logo design needed", "branding"),
            ("Brand identity help", "branding"),
            ("Branding services required", "branding"),
            
            # Chatbot services
            ("Chatbot development", "chatbot"),
            ("AI assistant needed", "chatbot"),
            ("Customer service automation", "chatbot"),
            
            # Payment services
            ("Payment gateway integration", "payment"),
            ("Stripe setup needed", "payment"),
            ("PayPal integration", "payment"),
            
            # Automation services
            ("Business automation", "automation"),
            ("Workflow optimization", "automation"),
            ("Process automation", "automation"),
        ]
        
        # Generate 2000 variations
        for i in range(80):
            for scenario, expected_service in service_scenarios:
                variations = [
                    scenario,
                    f"I'm looking for {scenario.lower()}",
                    f"Can you help with {scenario.lower()}?",
                    f"Do you offer {scenario.lower()}?",
                    f"I want {scenario.lower()}",
                ]
                
                for variation in variations:
                    self.test_single_scenario(variation, "service_detection", expected_service)
                    
    def test_appointment_scenarios(self):
        """Test appointment triggering - 1000 tests"""
        appointment_triggers = [
            "I want to schedule an appointment",
            "Can we book a meeting?",
            "Schedule consultation please",
            "Book appointment for tomorrow",
            "I need to schedule a call",
            "Let's book a meeting",
            "Can we schedule time to talk?",
            "I want to book consultation",
        ]
        
        non_appointment_phrases = [
            "I want social media marketing",
            "Tell me about your services",
            "How are you doing?",
            "What do you offer?",
            "I need help with website",
            "Can you help me?",
            "I have a business",
            "Marketing services needed",
        ]
        
        # Test appointment triggers (should trigger)
        for i in range(50):
            for trigger in appointment_triggers:
                self.test_single_scenario(trigger, "appointment_trigger", True)
                
        # Test non-appointment phrases (should NOT trigger)
        for i in range(50):
            for phrase in non_appointment_phrases:
                self.test_single_scenario(phrase, "appointment_trigger", False)
                
    def test_conversation_flow(self):
        """Test conversation flow - 1500 tests"""
        conversation_flows = [
            # Business + Service flow
            [
                ("I have an electronics showroom", "business_detection"),
                ("I need marketing help", "service_detection"),
                ("What services do you offer?", "service_list"),
                ("I want social media marketing", "specific_service"),
                ("Schedule appointment please", "appointment_trigger")
            ],
            
            # Service inquiry flow
            [
                ("What services do you provide?", "service_list"),
                ("Tell me about website development", "service_info"),
                ("How much does it cost?", "pricing_redirect"),
                ("Book consultation", "appointment_trigger")
            ],
            
            # Quick service request
            [
                ("I need a website for my restaurant", "mixed_scenario"),
                ("What's included?", "service_details"),
                ("Let's schedule a call", "appointment_trigger")
            ]
        ]
        
        for i in range(100):
            for flow in conversation_flows:
                for message, expected_type in flow:
                    self.test_single_scenario(message, expected_type, None)
                    time.sleep(0.1)  # Simulate conversation timing
                    
    def test_edge_cases(self):
        """Test edge cases and error scenarios - 1000 tests"""
        edge_cases = [
            # Empty/minimal input
            ("", "error_handling"),
            ("hi", "greeting"),
            ("hello", "greeting"),
            ("?", "clarification"),
            
            # Inappropriate requests
            ("kidnapping business help", "inappropriate_rejection"),
            ("illegal services", "inappropriate_rejection"),
            ("hacking services", "inappropriate_rejection"),
            
            # Nonsensical input
            ("asdfghjkl", "clarification"),
            ("123456789", "clarification"),
            ("random gibberish text", "clarification"),
            
            # Very long input
            ("I have a very long business description " * 50, "long_input_handling"),
            
            # Special characters
            ("I need help with my business!@#$%^&*()", "special_chars"),
            ("café restaurant help", "unicode_handling"),
            
            # Multiple services
            ("I need website, social media, and branding", "multiple_services"),
            ("Website + marketing + chatbot needed", "multiple_services"),
        ]
        
        for i in range(50):
            for case, expected_handling in edge_cases:
                self.test_single_scenario(case, expected_handling, None)
                
    def test_performance_stress(self):
        """Test performance under load - 1500 tests"""
        print("🔥 STRESS TESTING - Simulating high load...")
        
        # Concurrent requests
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = []
            
            for i in range(150):
                test_messages = [
                    "I need marketing for my electronics showroom",
                    "Website development services",
                    "Social media marketing help",
                    "Schedule appointment please",
                    "What services do you offer?",
                    "Branding services needed",
                    "Chatbot development",
                    "Payment gateway integration",
                    "Business automation help",
                    "I have a restaurant business"
                ]
                
                for message in test_messages:
                    future = executor.submit(self.test_single_scenario, message, "stress_test", None)
                    futures.append(future)
                    
            # Wait for all tests to complete
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    print(f"Stress test error: {e}")
                    
    def test_real_world_scenarios(self):
        """Test real-world user scenarios - 2000 tests"""
        real_scenarios = [
            # Actual user messages
            "Hi, I run a small electronics store and need help with online marketing",
            "Hello, I'm looking for someone to create a website for my restaurant",
            "Good morning, can you help me with social media for my boutique?",
            "I need a chatbot for my dental clinic",
            "My agency needs rebranding services",
            "Can you integrate payment gateway for my online store?",
            "I want to automate my business processes",
            "Help me grow my Instagram followers for my salon",
            "Need a professional website for my law firm",
            "Looking for digital marketing services for my gym",
            
            # Complex scenarios
            "I have multiple businesses - a restaurant and a retail store, need comprehensive marketing",
            "My electronics showroom is struggling with online presence, need complete digital solution",
            "Starting a new business, need everything from website to social media",
            "Expanding my clinic, need appointment booking system and marketing",
            "Rebranding my agency, need logo, website, and social media strategy",
        ]
        
        for i in range(100):
            for scenario in real_scenarios:
                # Test with variations
                variations = [
                    scenario,
                    f"Paul here. {scenario}",
                    f"Hi, I'm Sarah. {scenario}",
                    f"Good day! {scenario}",
                    f"Can you help? {scenario}",
                ]
                
                for variation in variations:
                    self.test_single_scenario(variation, "real_world", None)
                    
    def test_error_handling(self):
        """Test error handling - 1000 tests"""
        error_scenarios = [
            # Network simulation
            ("Test message", "timeout_simulation"),
            
            # Invalid JSON (simulated)
            ("Malformed request test", "json_error"),
            
            # Server overload simulation
            ("Heavy load test", "server_stress"),
        ]
        
        for i in range(300):
            for scenario, error_type in error_scenarios:
                self.test_single_scenario(scenario, error_type, None)
                
    def test_single_scenario(self, message: str, test_type: str, expected_result: Any = None):
        """Test a single scenario and record results"""
        start_time = time.time()
        
        try:
            # Send request to chatbot
            response = requests.post(
                self.chat_endpoint,
                json={"message": message, "user_name": "TestUser"},
                timeout=10
            )
            
            response_time = time.time() - start_time
            self.performance_metrics['response_time_total'] += response_time
            self.performance_metrics['total_tests'] += 1
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '').lower()
                
                # Analyze response quality
                is_intelligent = self.analyze_response_intelligence(message, response_text, test_type, expected_result)
                
                if is_intelligent:
                    self.performance_metrics['intelligent_responses'] += 1
                    
                self.performance_metrics['successful_responses'] += 1
                
                # Record detailed result
                self.test_results.append({
                    'message': message,
                    'response': response_text,
                    'test_type': test_type,
                    'expected': expected_result,
                    'intelligent': is_intelligent,
                    'response_time': response_time,
                    'status': 'success'
                })
                
            else:
                self.performance_metrics['errors'] += 1
                self.test_results.append({
                    'message': message,
                    'test_type': test_type,
                    'status': 'error',
                    'error_code': response.status_code
                })
                
        except Exception as e:
            self.performance_metrics['errors'] += 1
            self.performance_metrics['total_tests'] += 1
            self.test_results.append({
                'message': message,
                'test_type': test_type,
                'status': 'exception',
                'error': str(e)
            })
            
    def analyze_response_intelligence(self, message: str, response: str, test_type: str, expected: Any) -> bool:
        """Analyze if response shows intelligence"""
        message_lower = message.lower()
        
        # Check for generic template responses (BAD)
        generic_templates = [
            "thank you for your message",
            "i'm here to help you with techrypt.io's digital services",
            "could you tell me more about what you're looking for"
        ]
        
        if any(template in response for template in generic_templates):
            return False  # Generic response = not intelligent
            
        # Check for business intelligence
        if test_type == "business_detection":
            if expected == "electronics" and "electronics" in message_lower:
                return "electronics" in response or "showroom" in response
                
        # Check for service intelligence
        if test_type == "service_detection":
            if "marketing" in message_lower:
                return "marketing" in response or "social media" in response
                
        # Check for appointment intelligence
        if test_type == "appointment_trigger":
            appointment_words = ["schedule", "appointment", "book", "meeting", "consultation"]
            if expected and any(word in message_lower for word in appointment_words):
                return "appointment" in response or "schedule" in response
                
        # General intelligence indicators
        intelligence_indicators = [
            len(response) > 50,  # Substantial response
            "techrypt" in response,  # Company awareness
            any(service in response for service in ["website", "social media", "branding", "chatbot", "automation", "payment"]),
            "help" in response or "assist" in response,
            response.count('.') >= 2,  # Multiple sentences
        ]
        
        return sum(intelligence_indicators) >= 3
        
    def generate_performance_report(self):
        """Generate comprehensive performance report"""
        total = self.performance_metrics['total_tests']
        successful = self.performance_metrics['successful_responses']
        intelligent = self.performance_metrics['intelligent_responses']
        errors = self.performance_metrics['errors']
        
        success_rate = (successful / total * 100) if total > 0 else 0
        intelligence_rate = (intelligent / total * 100) if total > 0 else 0
        error_rate = (errors / total * 100) if total > 0 else 0
        avg_response_time = (self.performance_metrics['response_time_total'] / total) if total > 0 else 0
        
        print("\n" + "="*80)
        print("🎯 COMPREHENSIVE CHATBOT PERFORMANCE REPORT")
        print("="*80)
        print(f"📊 Total Tests Executed: {total:,}")
        print(f"✅ Successful Responses: {successful:,} ({success_rate:.2f}%)")
        print(f"🧠 Intelligent Responses: {intelligent:,} ({intelligence_rate:.2f}%)")
        print(f"❌ Errors: {errors:,} ({error_rate:.2f}%)")
        print(f"⚡ Average Response Time: {avg_response_time:.3f}s")
        print(f"🎯 OVERALL PERFORMANCE SCORE: {intelligence_rate:.2f}%")
        
        if intelligence_rate >= 97:
            print("🎉 EXCELLENT! Target 97%+ performance ACHIEVED!")
        else:
            print(f"⚠️  NEEDS IMPROVEMENT! Target: 97%, Current: {intelligence_rate:.2f}%")
            
        # Save detailed results
        with open('chatbot_test_results.json', 'w') as f:
            json.dump({
                'metrics': self.performance_metrics,
                'detailed_results': self.test_results[:100]  # Save first 100 for analysis
            }, f, indent=2)
            
        print(f"📁 Detailed results saved to: chatbot_test_results.json")
        print("="*80)

if __name__ == "__main__":
    tester = ComprehensiveChatbotTester()
    tester.run_comprehensive_test_suite()
