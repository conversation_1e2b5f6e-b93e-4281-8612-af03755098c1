#!/usr/bin/env python3
"""
Test Specific Service Detection
"""

import requests
import json

def test_specific_services():
    """Test if business intelligence detects specific services correctly"""
    
    base_url = "http://localhost:5000"
    
    test_cases = [
        {
            "input": "services",
            "description": "Should show structured service list"
        },
        {
            "input": "daraz store",
            "description": "Should detect Daraz business platform"
        },
        {
            "input": "I need help with my website",
            "description": "Should detect website development need"
        },
        {
            "input": "how to grow Instagram followers",
            "description": "Should detect social media marketing need"
        },
        {
            "input": "I need a logo",
            "description": "Should detect branding services need"
        },
        {
            "input": "hello",
            "description": "Should provide business greeting"
        },
        {
            "input": "tell me a joke",
            "description": "Should redirect to business services"
        }
    ]
    
    print("🎯 Testing Specific Service Detection")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/{len(test_cases)}:")
        print(f"Input: '{test_case['input']}'")
        print(f"Description: {test_case['description']}")
        
        try:
            payload = {"message": test_case['input'], "name": ""}
            response = requests.post(f"{base_url}/chat", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '')
                
                print(f"Response: {ai_response[:300]}...")
                
                # Check response type
                if "Here are our main services:" in ai_response:
                    print("✅ STRUCTURED SERVICE LIST")
                elif "Great!" in ai_response and "store" in ai_response:
                    print("✅ BUSINESS PLATFORM DETECTED")
                elif "I can help you with website development" in ai_response:
                    print("✅ WEBSITE SERVICE DETECTED")
                elif "Social Media Marketing" in ai_response and "grow" in ai_response:
                    print("✅ SOCIAL MEDIA SERVICE DETECTED")
                elif "Branding Services" in ai_response and "logo" in ai_response:
                    print("✅ BRANDING SERVICE DETECTED")
                elif "Hello" in ai_response and "Welcome" in ai_response:
                    print("✅ BUSINESS GREETING")
                elif "business and digital marketing services" in ai_response:
                    print("✅ BUSINESS REDIRECT")
                else:
                    print("⚠️ GENERIC RESPONSE")
                    
            else:
                print(f"❌ ERROR - HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ ERROR - {e}")
        
        print("-" * 60)

if __name__ == "__main__":
    test_specific_services()
